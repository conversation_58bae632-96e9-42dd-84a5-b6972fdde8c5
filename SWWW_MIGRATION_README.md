# Migración de swaybg a swww

## Cambios realizados

### 1. Script AleatoryWall.sh modificado
- **Dependencia cambiada**: `swaybg` → `swww`
- **Nueva función**: `ensure_swww_daemon()` en lugar de `kill_swaybg()`
- **Comando de wallpaper**: `swaybg -i "$wallpaper" -m fill` → `swww img "$wallpaper" --transition-type wipe --transition-duration 2`
- **Gestión de procesos**: Ya no mata procesos swaybg, solo asegura que swww-daemon esté corriendo

### 2. Script wallpaper-debug.sh actualizado
- **Dependencia verificada**: `swaybg` → `swww`
- **Proceso verificado**: `swaybg` → `swww-daemon`

### 3. Autostart actualizado
- **Agregado**: `exec-once = swww-daemon &` para iniciar el daemon automáticamente

### 4. Nuevo script swww-control.sh
Script completo para control manual de swww con las siguientes funciones:
- `start`: Iniciar daemon
- `stop`: Detener daemon  
- `restart`: Reiniciar daemon
- `status`: Ver estado del daemon
- `random`: Cambiar a wallpaper aleatorio
- `list`: Listar wallpapers disponibles
- `current`: Ver wallpaper actual

## Ventajas de swww sobre swaybg

1. **Transiciones animadas**: swww soporta transiciones suaves entre wallpapers
2. **Mejor rendimiento**: Un solo daemon vs múltiples procesos swaybg
3. **Más opciones**: Diferentes tipos de transición (wipe, fade, etc.)
4. **Mejor integración**: Diseñado específicamente para Wayland

## Uso

### Scripts existentes (siguen funcionando igual):
```bash
# Cambiar wallpaper ahora
~/.config/hypr/scripts/tools/wallpaper-control.sh change

# Ver estado
~/.config/hypr/scripts/tools/wallpaper-control.sh status

# Ver logs
~/.config/hypr/scripts/tools/wallpaper-control.sh logs
```

### Nuevo script swww-control.sh:
```bash
# Control del daemon
~/.config/hypr/scripts/tools/swww-control.sh start
~/.config/hypr/scripts/tools/swww-control.sh stop
~/.config/hypr/scripts/tools/swww-control.sh restart
~/.config/hypr/scripts/tools/swww-control.sh status

# Gestión de wallpapers
~/.config/hypr/scripts/tools/swww-control.sh random
~/.config/hypr/scripts/tools/swww-control.sh list
~/.config/hypr/scripts/tools/swww-control.sh current
```

## Instalación de swww

Si no tienes swww instalado:

### Arch Linux:
```bash
sudo pacman -S swww
```

### Otras distribuciones:
```bash
# Desde cargo (Rust)
cargo install swww

# O compilar desde fuente
git clone https://github.com/Horus645/swww.git
cd swww
cargo build --release
```

## Verificación

Para verificar que todo funciona correctamente:

1. **Verificar dependencias**:
   ```bash
   ~/.config/hypr/scripts/tools/wallpaper-debug.sh
   ```

2. **Probar cambio manual**:
   ```bash
   ~/.config/hypr/scripts/tools/swww-control.sh random
   ```

3. **Verificar daemon automático**:
   ```bash
   ~/.config/hypr/scripts/tools/swww-control.sh status
   ```

## Configuración de transiciones

Puedes personalizar las transiciones editando el archivo `AleatoryWall.sh` en la línea:
```bash
swww img "$wallpaper" --transition-type wipe --transition-duration 2
```

Tipos de transición disponibles:
- `simple`: Sin transición
- `fade`: Desvanecimiento
- `wipe`: Barrido (por defecto)
- `slide`: Deslizamiento
- `grow`: Crecimiento
- `outer`: Desde afuera

## Troubleshooting

### Si el daemon no inicia:
```bash
# Matar procesos existentes
pkill -x swww-daemon
# Reiniciar
~/.config/hypr/scripts/tools/swww-control.sh start
```

### Si no cambia el wallpaper:
```bash
# Verificar que el daemon esté corriendo
pgrep -x swww-daemon
# Si no está corriendo, iniciarlo
swww-daemon &
```

### Para volver a swaybg temporalmente:
1. Comentar `exec-once = swww-daemon &` en `config/autostart.conf`
2. Revertir cambios en `AleatoryWall.sh` (cambiar `swww` por `swaybg`)
3. Reiniciar Hyprland
