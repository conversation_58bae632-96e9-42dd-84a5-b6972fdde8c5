# Fondo estilo glass con blur
background {
    path = ~/Wallpapers/WALL4.jpg  # Usa aquí el mismo fondo que tienes con `swww`
 #   blur = true
    blur_passes = 3
    blur_size = 7
    noise = 0.02
    contrast = 1.0
    brightness = 0.9
    vibrancy = 0.3
}

# Input de contraseña centrado
input-field {
    monitor = eDP-1  # Cámbialo a tu monitor si no es eDP-1 (usa `hyprctl monitors`)
    size = 250, 60
    outline_thickness = 3
    dots_size = 0.2
    dots_spacing = 0.3
    dots_center = true
    fade_on_empty = true
    font_family = JetBrainsMono Nerd Font
    inner_color = rgba(30, 30, 30, 0.4)
    outer_color = rgba(255, 255, 255, 0.2)
   ## outline_color = rgba(255, 255, 255, 0.3)
    placeholder_text = Password...
    position = 0, 100  # X=0 (centro), Y=+100 desde centro vertical
    halign = center
    valign = center
}

# Reloj dinámico en el centro
label {
    monitor = eDP-1
    text = $TIME
    font_family = JetBrainsMono Nerd Font
    font_size = 36
    position = 0, -100  # X=0 (centro), Y=-100 desde centro vertical
    halign = center
    valign = center
    color = rgba(255, 255, 255, 0.85)
}

