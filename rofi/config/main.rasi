/**
 *
 * Author : <PERSON><PERSON><PERSON> (adi1090x)
 * Github : @adi1090x
 *
 * Configuration For Rofi Version: 1.7.3
 **/

configuration {
	/*---------- General setting ----------*/
	modi: "drun,run,filebrowser,window";
	case-sensitive: false;
	cycle: true;
	filter: "";
	scroll-method: 0;
	normalize-match: true;
	show-icons: true;
	icon-theme: "Papirus";
/*	cache-dir: ;*/
	steal-focus: false;
/*	dpi: -1;*/

	/*---------- Matching setting ----------*/
	matching: "normal";
	tokenize: true;

	/*---------- SSH settings ----------*/
	ssh-client: "ssh";
	ssh-command: "{terminal} -e {ssh-client} {host} [-p {port}]";
	parse-hosts: true;
	parse-known-hosts: true;

	/*---------- Module Imports ----------*/
	@import "modules/drun.rasi"
	@import "modules/window.rasi"
	@import "modules/filebrowser.rasi"

	/*---------- Run settings ----------*/
	run-command: "{cmd}";
	run-list-command: "";
	run-shell-command: "{terminal} -e {cmd}";

	/*---------- Combi settings ----------*/
/*	combi-modi: "window,run";*/
/*	combi-hide-mode-prefix: false;*/
/*	combi-display-format: "{mode} {text}";*/

	/*---------- History and Sorting ----------*/
	disable-history: false;
	sorting-method: "normal";
	max-history-size: 25;

	/*---------- Display setting ----------*/
	display-run: "Run";
	display-ssh: "SSH";
	display-combi: "Combi";
	display-keys: "Keys";

	/*---------- Misc setting ----------*/
	terminal: "rofi-sensible-terminal";
	font: "Mono 12";
	sort: false;
	threads: 0;
	click-to-exit: true;
/*	ignored-prefixes: "";*/
/*	pid: "/run/user/1000/rofi.pid";*/



	/*---------- Other settings ----------*/
    timeout {
      action: "kb-cancel";
      delay:  0;
    }

	/*---------- Keybindings ----------*/
	/* Keybindings moved to config/keybindings.rasi */
	@import "keybindings.rasi"
}
