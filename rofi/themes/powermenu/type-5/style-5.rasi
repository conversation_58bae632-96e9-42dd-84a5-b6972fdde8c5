/**
 *
 * Author : <PERSON><PERSON><PERSON> (adi1090x)
 * Github : @adi1090x
 * 
 * Rofi Theme File
 * Rofi Version: 1.7.3
 **/

/*****----- Configuration -----*****/
configuration {
    show-icons:                 false;
}

/*****----- Global Properties -----*****/
* {
    font:                        "JetBrains Mono Nerd Font 10";
    background:                  #11092D;
    background-alt:              #281657;
    foreground:                  #FFFFFF;
    selected:                    #DF5296;
    active:                      #6E77FF;
    urgent:                      #8E3596;
}

/*
USE_BUTTONS=YES
*/

/*****----- Main Window -----*****/
window {
    transparency:                "real";
    location:                    center;
    anchor:                      center;
    fullscreen:                  false;
    width:                       1000px;
    x-offset:                    0px;
    y-offset:                    0px;

    padding:                     0px;
    border:                      0px solid;
    border-radius:               20px;
    border-color:                @selected;
    cursor:                      "default";
    background-color:            @background;
}

/*****----- Main Box -----*****/
mainbox {
    enabled:                     true;
    spacing:                     0px;
    margin:                      0px;
    padding:                     0px;
    border:                      0px solid;
    border-radius:               0px;
    border-color:                @selected;
    background-color:            transparent;
    children:                    [ "inputbar", "listview", "message" ];
}

/*****----- Inputbar -----*****/
inputbar {
    enabled:                     true;
    spacing:                     20px;
    padding:                     100px 40px;
    background-color:            transparent;
    background-image:            url("~/.config/rofi/images/j.jpg", width);
    children:                    [ "textbox-prompt-colon", "prompt"];
}

dummy {
    background-color:            transparent;
}

textbox-prompt-colon {
    enabled:                     true;
    expand:                      false;
    str:                         " System";
    padding:                     15px;
    border:                      0px 0px 0px 10px;
    border-radius:               100% 100% 0px 100%;
    border-color:                @selected;
    background-color:            @urgent;
    text-color:                  @foreground;
}
prompt {
    enabled:                     true;
    padding:                     15px;
    border:                      0px;
    border-radius:               0px 100% 100% 100%;
    border-color:                @selected;
    background-color:            @active;
    text-color:                  @background;
}

/*****----- Listview -----*****/
listview {
    enabled:                     true;
    columns:                     6;
    lines:                       1;
    cycle:                       true;
    dynamic:                     true;
    scrollbar:                   false;
    layout:                      vertical;
    reverse:                     false;
    fixed-height:                true;
    fixed-columns:               true;
    
    spacing:                     30px;
    margin:                      30px;
    background-color:            transparent;
    cursor:                      "default";
}

/*****----- Elements -----*****/
element {
    enabled:                     true;
    padding:                     35px 10px;
    border-radius:               55px;
    background-color:            @background-alt;
    text-color:                  @foreground;
    cursor:                      pointer;
}
element-text {
    font:                        "feather bold 32";
    background-color:            transparent;
    text-color:                  inherit;
    cursor:                      inherit;
    vertical-align:              0.5;
    horizontal-align:            0.5;
}
element selected.normal {
    background-color:            var(selected);
    text-color:                  var(background);
}

/*****----- Message -----*****/
message {
    enabled:                     true;
    margin:                      0px;
    padding:                     15px;
    border-radius:               0px;
    background-color:            @background-alt;
    text-color:                  @foreground;
}
textbox {
    background-color:            inherit;
    text-color:                  inherit;
    vertical-align:              0.5;
    horizontal-align:            0.5;
}
