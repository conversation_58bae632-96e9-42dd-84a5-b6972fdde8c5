/**
 *
 * Author : <PERSON><PERSON><PERSON> (adi1090x)
 * Github : @adi1090x
 * 
 * Rofi Theme File
 * Rofi Version: 1.7.3
 **/

/*****----- Configuration -----*****/
configuration {
    show-icons:                 false;
}

/*****----- Global Properties -----*****/
* {
    font:                        "JetBrains Mono Nerd Font 10";
    background:                  #2D1B14;
    background-alt:              #462D23;
    foreground:                  #FFFFFF;
    selected:                    #E25F3E;
    active:                      #716251;
    urgent:                      #934A1C;
}

/*
USE_BUTTONS=YES
*/

/*****----- Main Window -----*****/
window {
    transparency:                "real";
    location:                    center;
    anchor:                      center;
    fullscreen:                  false;
    width:                       800px;
    x-offset:                    0px;
    y-offset:                    0px;

    padding:                     0px;
    border:                      0px solid;
    border-radius:               20px;
    border-color:                @selected;
    cursor:                      "default";
    background-color:            @background;
}

/*****----- Main Box -----*****/
mainbox {
    enabled:                     true;
    spacing:                     0px;
    margin:                      0px;
    padding:                     0px;
    border:                      0px solid;
    border-radius:               0px;
    border-color:                @selected;
    background-color:            transparent;
    children:                    [ "inputbar", "listview", "message" ];
}

/*****----- Inputbar -----*****/
inputbar {
    enabled:                     true;
    spacing:                     0px;
    padding:                     100px 80px;
    background-color:            transparent;
    background-image:            url("~/.config/rofi/images/d.png", width);
    children:                    [ "textbox-prompt-colon", "dummy","prompt"];
}

dummy {
    background-color:            transparent;
}

textbox-prompt-colon {
    enabled:                     true;
    expand:                      false;
    str:                         " System";
    padding:                     12px;
    border-radius:               100%;
    background-color:            @urgent;
    text-color:                  @foreground;
}
prompt {
    enabled:                     true;
    padding:                     12px;
    border-radius:               100%;
    background-color:            @active;
    text-color:                  @foreground;
}

/*****----- Listview -----*****/
listview {
    enabled:                     true;
    columns:                     6;
    lines:                       1;
    cycle:                       true;
    dynamic:                     true;
    scrollbar:                   false;
    layout:                      vertical;
    reverse:                     false;
    fixed-height:                true;
    fixed-columns:               true;
    
    spacing:                     15px;
    margin:                      15px;
    background-color:            transparent;
    cursor:                      "default";
}

/*****----- Elements -----*****/
element {
    enabled:                     true;
    padding:                     28px 10px;
    border-radius:               100%;
    background-color:            @background-alt;
    text-color:                  @foreground;
    cursor:                      pointer;
}
element-text {
    font:                        "feather bold 32";
    background-color:            transparent;
    text-color:                  inherit;
    cursor:                      inherit;
    vertical-align:              0.5;
    horizontal-align:            0.5;
}
element selected.normal {
    background-color:            var(selected);
    text-color:                  var(background);
}

/*****----- Message -----*****/
message {
    enabled:                     true;
    margin:                      0px 15px 15px 15px;
    padding:                     15px;
    border-radius:               100%;
    background-color:            @background-alt;
    text-color:                  @foreground;
}
textbox {
    background-color:            inherit;
    text-color:                  inherit;
    vertical-align:              0.5;
    horizontal-align:            0.5;
}
