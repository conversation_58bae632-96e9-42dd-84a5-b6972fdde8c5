/*
 *
 * Author  : <PERSON><PERSON><PERSON>
 * Mail    : <EMAIL>
 * Github  : @adi1090x
 * Twitter : @adi1090x
 * Edited for this particular configuration by <PERSON><PERSON><PERSON>.
 */

configuration {
	font:							"Museo Sans 18";
    show-icons:                     false;
	icon-theme: 					"Papirus";
    drun-display-format:            "{name}";
    disable-history:                false;
    fullscreen:                     false;
	hide-scrollbar: 				true;
	sidebar-mode: 					false;
}

@import "black_powermenu.rasi"

window {
    transparency:                   "real";
    background-color:               @background;
    text-color:                     @foreground;
    border-radius:                  12px;
    height:                         22%;
    width:                          37%;
    location:                       center;
    x-offset:                       0;
    y-offset:                       0;
}

prompt {
    enabled: 						false;
    padding:                        0.5% 0.5% 0.5% 0%;
	background-color: 				@background;
	text-color: 					@foreground;
}

textbox-prompt-colon {
	expand: 						true;
	str: 							"Goodbye, Axarva";
	background-color: 				@background;
	text-color: 					@selected;
    padding:                        3% 0.5% 0% 0%;
}

inputbar {
	children: 						[ textbox-prompt-colon ];
    background-color:               @background;
    text-color:                     @foreground;
    expand:                         false;
    border:                  		0% 0.2% 0.2% 0%;
    border-radius:                  0% 100% 100% 100%;
    border-color:                  	@border;
    margin:                         0% 3% 0% 9.5%;
    padding:                        0.5%;
    position:                       center;
}

listview {
    background-color:               @background;
    margin:                         0% 0% 0% 0%;
    spacing:                        1%;
    cycle:                          true;
    dynamic:                        true;
    layout:                         horizontal;
}

mainbox {
    background-color:               @background;
    children:                       [ inputbar, listview ];
    spacing:                        1.5%;
    padding:                        0.1% 0% 0% 2%;
}

element {
    background-color:               @background-alt;
    text-color:                     @selected;
    orientation:                    horizontal;
    border-radius:                  100%;
}

element-text, element-icon {
    background-color: inherit;
    text-color:       inherit;
}

element-text {
	font:							"feather 28";
    expand:                         true;
    horizontal-align:               0.5;
    vertical-align:                 0.5;
    margin:                         2.5% 1.7% 2.5% 1.7%;
}

element normal.urgent,
element alternate.urgent {
    background-color:               @urgent;
    text-color:                     @foreground;
    border-radius:                  0.2%;
}

element normal.active,
element alternate.active {
    background-color:               @background-alt;
    text-color:                     @foreground;
}

element selected {
    background-color:               @background;
    text-color:                     @red;
    border:                  		0% 0.2% 0.2% 0%;
    border-radius:                  100%;
    border-color:                  	@border;
}

element selected.urgent {
    background-color:               @urgent;
    text-color:                     @foreground;
}

element selected.active {
    background-color:               @background-alt;
    color:                          @foreground;
}
