/**
 *
 * Author : <PERSON><PERSON><PERSON> (adi1090x)
 * Github : @adi1090x
 * 
 * Rofi Theme File
 * Rofi Version: 1.7.3
 **/

/*****----- Configuration -----*****/
configuration {
    show-icons:                 false;
}

/*****----- Global Properties -----*****/
@import                          "shared/colors.rasi"
@import                          "shared/fonts.rasi"

* {
    /* Resolution : 1920x1080 */
    box-spacing:                 50px;
    box-margin:                  300px 250px;
    box-padding:                 50px;
    message-margin:              0px 400px;
    inputbar-spacing:            0px;
    list-spacing:                0px;
    general-padding:             20px;
    element-padding:             90px 10px;
    element-radius:              80px;
    general-radius:              100%;
    element-font:                "feather 48";
}

/*****----- Main Window -----*****/
window {
    /* properties for window widget */
    transparency:                "real";
    location:                    center;
    anchor:                      center;
    fullscreen:                  true;
    width:                       1366px;
    x-offset:                    0px;
    y-offset:                    0px;

    /* properties for all widgets */
    enabled:                     true;
    margin:                      0px;
    padding:                     0px;
    border:                      0px solid;
    border-radius:               0px;
    border-color:                @selected;
    cursor:                      "default";
    background-color:            @background;
}

/*****----- Main Box -----*****/
mainbox {
    enabled:                     true;
    spacing:                     var(box-spacing);
    margin:                      var(box-margin);
    padding:                     var(box-padding);
    border:                      0px solid;
    border-radius:               var(element-radius);
    border-color:                @selected;
    background-color:            @background-alt;
    children:                    [ "inputbar", "listview" ];
}

/*****----- Inputbar -----*****/
inputbar {
    enabled:                     true;
    spacing:                     var(inputbar-spacing);
    margin:                      0px;
    padding:                     0px;
    border:                      0px;
    border-radius:               0px;
    border-color:                @selected;
    background-color:            transparent;
    text-color:                  @foreground;
    children:                    [ "textbox-prompt-colon", "dummy", "prompt" ];
}

dummy {
    background-color:            transparent;
}

textbox-prompt-colon {
    enabled:                     true;
    expand:                      false;
    str:                         "SYSTEM";
    padding:                     var(general-padding);
    border-radius:               var(general-radius);
    background-color:            @background;
    text-color:                  @urgent;
}
prompt {
    enabled:                     true;
    padding:                     var(general-padding);
    border-radius:               var(general-radius);
    background-color:            @background;
    text-color:                  @active;
}

/*****----- Message -----*****/
message {
    enabled:                     true;
    margin:                      var(message-margin);
    padding:                     var(general-padding);
    border:                      0px;
    border-radius:               var(general-radius);
    border-color:                @selected;
    background-color:            @background-alt;
    text-color:                  @foreground;
}
textbox {
    background-color:            inherit;
    text-color:                  inherit;
    vertical-align:              0.5;
    horizontal-align:            0.5;
    placeholder-color:           @foreground;
    blink:                       true;
    markup:                      true;
}
error-message {
    padding:                     var(general-padding);
    border:                      0px solid;
    border-radius:               var(general-radius);
    border-color:                @selected;
    background-color:            @background;
    text-color:                  @foreground;
}

/*****----- Listview -----*****/
listview {
    enabled:                     true;
    columns:                     5;
    lines:                       1;
    cycle:                       true;
    dynamic:                     true;
    scrollbar:                   false;
    layout:                      vertical;
    reverse:                     false;
    fixed-height:                true;
    fixed-columns:               true;
    
    spacing:                     var(list-spacing);
    margin:                      0px;
    padding:                     0px;
    border:                      0px solid;
    border-radius:               var(element-radius);
    border-color:                @selected;
    background-color:            @background;
    text-color:                  @foreground;
    cursor:                      "default";
}

/*****----- Elements -----*****/
element {
    enabled:                     true;
    spacing:                     0px;
    margin:                      0px;
    padding:                     var(element-padding);
    border:                      0px solid;
    border-radius:               var(element-radius);
    border-color:                @selected;
    background-color:            transparent;
    text-color:                  @foreground;
    cursor:                      pointer;
}
element-text {
    font:                        var(element-font);
    background-color:            transparent;
    text-color:                  inherit;
    cursor:                      inherit;
    vertical-align:              0.5;
    horizontal-align:            0.5;
}
element selected.normal {
    background-color:            var(selected);
    text-color:                  var(background);
}
