/**
 *
 * Author : <PERSON><PERSON><PERSON> (adi1090x)
 * Github : @adi1090x
 * 
 * Rofi Theme File
 * Rofi Version: 1.7.3
 **/

/*****----- Configuration -----*****/
configuration {
    show-icons:                 false;
}

/*****----- Global Properties -----*****/
@import                          "shared/colors.rasi"
@import                          "shared/fonts.rasi"

* {
    /* Resolution : 1920x1080 */
    box-spacing:                 50px;
    box-margin:                  370px 350px;
    message-margin:              0px 350px;
    inputbar-spacing:            0px;
    list-spacing:                50px;
    general-padding:             20px;
    element-padding:             55px 10px;
    element-radius:              20px;
    general-radius:              100%;
    element-font:                "feather 48";
}

/*****----- Main Window -----*****/
window {
    /* properties for window widget */
    transparency:                "real";
    location:                    center;
    anchor:                      center;
    fullscreen:                  true;
    width:                       1366px;
    x-offset:                    0px;
    y-offset:                    0px;

    /* properties for all widgets */
    enabled:                     true;
    margin:                      0px;
    padding:                     0px;
    border:                      0px solid;
    border-radius:               0px;
    border-color:                @selected;
    cursor:                      "default";
    background-color:            @background;
}

/*****----- Main Box -----*****/
mainbox {
    enabled:                     true;
    spacing:                     var(box-spacing);
    margin:                      0px;
    padding:                     var(box-margin);
    border:                      0px solid;
    border-radius:               0px;
    border-color:                @selected;
    background-color:            transparent;
    children:                    [ "message", "listview" ];
}

/*****----- Inputbar -----*****/
inputbar {
    enabled:                     true;
    spacing:                     var(inputbar-spacing);
    margin:                      0px;
    padding:                     0px;
    border:                      0px;
    border-radius:               0px;
    border-color:                @selected;
    background-color:            transparent;
    text-color:                  @foreground;
    children:                    [ "dummy", "textbox-prompt-colon", "prompt", "dummy"];
}

dummy {
    background-color:            transparent;
}

textbox-prompt-colon {
    enabled:                     true;
    expand:                      false;
    str:                         "SYSTEM";
    padding:                     var(general-padding);
    border-radius:               var(general-radius);
    background-color:            @urgent;
    text-color:                  @background;
}
prompt {
    enabled:                     true;
    padding:                     var(general-padding);
    border-radius:               var(general-radius);
    background-color:            @active;
    text-color:                  @background;
}

/*****----- Message -----*****/
message {
    enabled:                     true;
    margin:                      var(message-margin);
    padding:                     var(general-padding);
    border:                      0px;
    border-radius:               var(general-radius);
    border-color:                @selected;
    background-color:            @background-alt;
    text-color:                  @foreground;
}
textbox {
    background-color:            inherit;
    text-color:                  inherit;
    vertical-align:              0.5;
    horizontal-align:            0.5;
    placeholder-color:           @foreground;
    blink:                       true;
    markup:                      true;
}
error-message {
    padding:                     var(general-padding);
    border:                      0px solid;
    border-radius:               var(general-radius);
    border-color:                @selected;
    background-color:            @background;
    text-color:                  @foreground;
}

/*****----- Listview -----*****/
listview {
    enabled:                     true;
    columns:                     5;
    lines:                       1;
    cycle:                       true;
    dynamic:                     true;
    scrollbar:                   false;
    layout:                      vertical;
    reverse:                     false;
    fixed-height:                true;
    fixed-columns:               true;
    
    spacing:                     var(list-spacing);
    margin:                      0px;
    padding:                     0px;
    border:                      0px solid;
    border-radius:               0px;
    border-color:                @selected;
    background-color:            transparent;
    text-color:                  @foreground;
    cursor:                      "default";
}

/*****----- Elements -----*****/
element {
    enabled:                     true;
    spacing:                     0px;
    margin:                      0px;
    padding:                     var(element-padding);
    border:                      0px solid;
    border-radius:               var(element-radius);
    border-color:                @selected;
    background-color:            @background-alt;
    text-color:                  @foreground;
    cursor:                      pointer;
}
element-text {
    font:                        var(element-font);
    background-color:            transparent;
    text-color:                  inherit;
    cursor:                      inherit;
    vertical-align:              0.5;
    horizontal-align:            0.5;
}
element selected.normal {
    background-color:            var(selected);
    text-color:                  var(background);
}
