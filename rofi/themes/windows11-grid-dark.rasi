* {
  font: "Roboto 10";

  bg0    : #1f1f1f80;
  bg1    : #202020bf;
  bg2    : #2c2c2c;
  bg3    : #393939bf;
  fg0    : #ffffff;
  fg1    : #cecece;
  accent : #60cdff;
  urgent : @accent;

  background-color : transparent;
  text-color       : @fg0;

  margin  : 0;
  padding : 0;
  spacing : 0;
}

element-icon, element-text, scrollbar {
  cursor: pointer;
}

window {
  location : south;
  width    : 600px;
  height   : 600px;
  y-offset : -4px;

  background-color : @bg1;
  border-radius    : 8px;
}

mainbox {
  padding : 24px;
  spacing : 24px;
}

inputbar {
  padding          : 8px;
  spacing          : 4px;
  children         : [ icon-search, entry ];
  border           : 0 0 2px 0 solid;
  border-color     : @accent;
  border-radius    : 2px;
  background-color : @bg0;
}

icon-search, entry, element-icon, element-text {
  vertical-align: 0.5;
}

icon-search {
  expand   : false;
  filename : "search-symbolic";
  size     : 24px;
}

entry {
  font              : "Roboto 12";
  placeholder       : "Type here to search";
  placeholder-color : @fg1;
}

textbox {
  padding          : 4px 8px;
  background-color : @bg2;
}

listview {
  columns       : 6;
  spacing       : 8px;
  fixed-height  : true;
  fixed-columns : true;
}

element {
  orientation   : vertical;
  spacing       : 4px;
  padding       : 8px;
  border-radius : 2px;
}

element normal urgent {
  text-color: @urgent;
}

element normal active {
  text-color: @accent;
}

element selected {
  background-color: @bg3;
}

element selected urgent {
  background-color: @urgent;
}

element-icon {
  size: 2em;
}

element-text {
  text-color       : inherit;
  horizontal-align : 0.5;
}
