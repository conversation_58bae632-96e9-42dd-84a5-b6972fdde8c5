/* Copyright (C) 2020-2021 <PERSON><PERSON><PERSON> <<EMAIL>> */
/* Everyone is permitted to copy and distribute copies of this file under GNU-GPL3 */

configuration {
    font:                           "Iosevka 10";
    show-icons:                     false;
    display-drun: 					"";
    drun-display-format:            "{icon} {name}";
    disable-history:                false;
	click-to-exit: 					true;
}

@import "colors.rasi"
@import "font.rasi"

window {
    transparency:                   "real";
    background-color:               @background;
    text-color:                     @foreground;
    width:                          300px;
    location:                       center;
    anchor:                         center;
    x-offset:                       0;
    y-offset:                       0;
}

prompt {
    enabled: 						true;
    margin:     					0px 0px 0px 0px;
    padding:    					6px 0px 6px 0px;
	background-color: 				@background;
	text-color: 					@foreground;
}

textbox-prompt-colon {
	expand: 						false;
	str: 							"";
    border-radius:                  10px;
    background-color:               @selected;
    text-color:                     @background;
    padding:    					4px 10px 6px 10px;

}

entry {
    background-color:               @background;
    text-color:                     @background;
    placeholder-color:              @background;
    placeholder:                    "";
    expand:                         true;
    horizontal-align:               0;
    blink:                          false;
    padding:                        6px;
}

dummy {
    background-color:            transparent;
}

inputbar {
	children: 						[ prompt, dummy, textbox-prompt-colon ];
    spacing:                        0;
    background-color:               @background;
    text-color:                     @foreground;
    expand:                         false;
    margin:                         0px 0px 0px 0px;
    padding:                        0px;
    position:                       center;
}

case-indicator {
    background-color:               @background;
    text-color:                     @foreground;
    spacing:                        0;
}


listview {
    background-color:               @background;
    columns:                        1;
    lines:							5;
    spacing:                        4px;
    cycle:                          true;
    dynamic:                        true;
    layout:                         vertical;
}

mainbox {
    background-color:               @background;
    children:                       [ inputbar, listview ];
    spacing:                       	10px;
    padding:                        30px;
}

element {
    background-color:               @background;
    text-color:                     @foreground;
    orientation:                    horizontal;
    border-radius:                  10px;
    padding:                        5px;
}

element-icon {
    background-color: 				inherit;
    text-color:       				inherit;
    horizontal-align:               0.5;
    vertical-align:                 0.5;
    size:                           0px;
    border:                         0px;
}

element-text {
    background-color: 				inherit;
    text-color:       				inherit;
    expand:                         true;
    horizontal-align:               0;
    vertical-align:                 0.5;
    margin:                         2px 0px 2px 2px;
}

element selected {
    background-color:               @selected;
    text-color:                     @background;
    border:                  		0px;
    border-radius:                  10px;
    border-color:                  	@selected;
}

element.active,
element.selected.urgent {
  background-color: @on;
  text-color: @background;
  border-color: @on;
}

element.selected.urgent {
  border-color: @selected;
}

element.urgent,
element.selected.active {
  background-color: @off;
  text-color: @background;
  border-color: @off;
}

element.selected.active {
  border-color: @selected;
}
