/*
 *
 * Author  : <PERSON><PERSON><PERSON>
 * Mail    : <EMAIL>
 * Github  : @adi1090x
 * Twitter : @adi1090x
 * Edited for this particular configuration by <PERSON><PERSON><PERSON>.
 */

configuration {
    lines:							10;
    columns:						1;
    font: 							"Museo Sans 12";
    bw: 							0;
    location: 						0;
    padding: 						0;
    fixed-num-lines: 				true;
    show-icons: 					true;
    icon-theme:						"candy-icons";
    sidebar-mode: 					true;
    separator-style: 				"none";
    hide-scrollbar: 				true;
    fullscreen: 					false;
    fake-transparency: 				false;
    scroll-method: 					0;
    window-format: 					"[{w}] ··· {c} ···   {t}";
    click-to-exit: 					true;
    show-match: 					false;
    combi-hide-mode-prefix: 		false;
    display-window: 				" ";
    display-windowcd: 				" ";
    display-run: 					" ";
    display-ssh: 					" ";
    display-drun: 					" ";
    display-combi: 					" ";
}

@import "black_launcher.rasi"

* {
    background-color:             	@bg;
}

window {
    border: 						0px;
    border-color: 					@ac;
    border-radius: 					12px;
    padding: 						30;
    width: 							35%;
}

prompt {
    spacing: 						0;
    border: 						0;
    text-color: 					@fg;
}

textbox-prompt-colon {
    expand: 						false;
    str: 							" ";
    margin:							0px 4px 0px 0px;
    text-color: 					inherit;
}

entry {
    spacing:    					0;
    text-color: 					@fg;
}

case-indicator {
    spacing:    					0;
    text-color: 					@fg;
}

inputbar {
    spacing:    					0px;
    text-color: 					@fg;
    padding:    					1px;
    children: 						[ prompt,textbox-prompt-colon,entry,case-indicator ];
}

mainbox {
    border: 						0px;
    border-color: 					@ac;
    padding: 						6;
}

listview {
    fixed-height: 					0;
    border: 						0px;
    border-color: 					@ac;
    spacing: 						4px;
    scrollbar: 						false;
    padding: 						5px 5px 0px 5px;
}

element {
    border: 						0px;
    border-radius: 					4px;
    padding: 						5px;
}
element-text, element-icon {
    background-color: inherit;
    text-color:       inherit;
}

element normal.normal {
    background-color: 				@bg;
    text-color:       				@fg;
}
element normal.urgent {
    background-color: 				@bg;
    text-color:       				@red;
}
element normal.active {
    background-color: 				@green;
    text-color:       				@bg;
}
element selected.normal {
    background-color: 				@fg;
    text-color:       				@bg;
}
element selected.urgent {
    background-color: 				@bg;
    text-color:       				@red;
}
element selected.active {
    background-color: 				@fg;
    text-color:       				@bg;
}
element alternate.normal {
    background-color: 				@bg;
    text-color:       				@fg;
}
element alternate.urgent {
    background-color: 				@bg;
    text-color:       				@fg;
}
element alternate.active {
    background-color: 				@bg;
    text-color:       				@fg;
}

element-text, element-icon {
    background-color:               inherit;
    text-color:                     inherit;
}

sidebar {
    border:       					0px;
    border-color: 					@ac;
    border-radius: 					0px;
}

button {
    background-color:             	@fg;
    margin: 						5px;
    padding: 						5px;
    text-color: 					@bg;
    border: 						0px;
    border-radius: 					4px;
    border-color: 					@fg;
}

button selected {
    background-color:             	@ac;
    text-color: 					@fg;
    border: 						0px;
    border-radius: 					4px;
    border-color: 					@fg;
}

scrollbar {
    width:        					4px;
    border:       					0px;
    handle-color: 					@fg;
    handle-width: 					8px;
    padding:      					0;
}

message {
    border: 						0px;
    border-color: 					@ac;
    padding: 						1px;
}

textbox {
    text-color: 					@fg;
}
