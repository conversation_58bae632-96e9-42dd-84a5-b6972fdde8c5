/* Copyright (C) 2020-2021 Ad<PERSON><PERSON> <<EMAIL>> */
/* Everyone is permitted to copy and distribute copies of this file under GNU-GPL3 */

configuration {
    font:                           "Iosevka 10";
    show-icons:                     false;
    disable-history:                false;
	click-to-exit: 					true;
}

@import "colors.rasi"
@import "font.rasi"

window {
    transparency:                   "real";
    background-color:               @background;
    text-color:                     @foreground;
    width:                          400px;
    height:                         820px;
    location:                       center;
    anchor:                         center;
    x-offset:                       0;
    y-offset:                       0;
}

prompt {
    enabled: 						false;
	margin: 						0px 0px 0px 8px;
    padding:    					6px 0px 6px 0px;
	background-color: 				@background;
	text-color: 					@foreground;
}

textbox-prompt-colon {
	expand: 						false;
	str: 							"直";
    border-radius:                  10px;
    background-color:               @highlight;
    text-color:                     @background;
    padding:    					6px 10px 6px 10px;
}

textbox-prompt-colon-2 {
	expand: 						false;
	str: 							"";
    background-color:               inherit;
    text-color:                     inherit;
    padding:    					5px 0px;
}

entry {
    background-color:               @background;
    text-color:                     @foreground;
    border-color:                  	@selected;
    placeholder-color:              @foreground;
    placeholder:                    " Search...";
    expand:                         true;
    horizontal-align:               0;
    blink:                          true;
    padding:                        6px;
}

inputbar {
	children: 						[ textbox-prompt-colon-2 , entry , textbox-prompt-colon];
    spacing:                        0;
    background-color:               @background;
    text-color:                     @foreground;
    expand:                         false;
    margin:                         0px 0px 0px 0px;
    padding:                        0px;
    position:                       center;
}

case-indicator {
    background-color:               @background;
    text-color:                     @foreground;
    spacing:                        0;
}


listview {
    background-color:               @background;
    columns:                        1;
    lines:							7;
    spacing:                        4px;
    cycle:                          true;
    dynamic:                        true;
    layout:                         vertical;
}

mainbox {
    background-color:               @background;
    children:                       [ inputbar, listview ];
    spacing:                       	10px;
    padding:                        30px;
}

element {
    background-color:               @background;
    text-color:                     @foreground;
    orientation:                    horizontal;
    border-radius:                  10px;
    padding:                        5px;
}

element-icon {
    background-color: 				inherit;
    text-color:       				inherit;
    horizontal-align:               0.5;
    vertical-align:                 0.5;
    size:                           0px;
    border:                         0px;
}

element-text {
    background-color: 				inherit;
    text-color:       				inherit;
    expand:                         true;
    horizontal-align:               0;
    vertical-align:                 0;
    margin:                         2px 0px 2px 2px;
}

element normal.urgent,
element alternate.urgent {
    background-color:               @urgent;
    text-color:                     @foreground;
}

element normal.active,
element alternate.active {
    background-color:               @background;
    text-color:                     @foreground;
}

element selected {
    background-color:               @selected;
    text-color:                     @background;
    border:                  		0px;
    border-color:                  	@selected;
}

element selected.urgent {
    background-color:               @urgent;
    text-color:                     @foreground;
}

element selected.active {
    background-color:               @background;
    color:                          @foreground;
}
