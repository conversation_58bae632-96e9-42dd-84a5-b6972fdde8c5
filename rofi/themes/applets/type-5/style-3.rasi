/**
 *
 * Author : <PERSON><PERSON><PERSON> (adi1090x)
 * Github : @adi1090x
 * 
 * Rofi Theme File
 * Rofi Version: 1.7.3
 **/

/*****----- Configuration -----*****/
configuration {
    show-icons:                 false;
}

/*****----- Global Properties -----*****/
* {
    font:                        "JetBrains Mono Nerd Font 10";
    background:                  #101010;
    background-alt:              #252525;
    foreground:                  #FFFFFF;
    selected:                    #505050;
    active:                      #909090;
    urgent:                      #707070;
}

/*
USE_ICON=NO
*/

/*****----- Main Window -----*****/
window {
    transparency:                "real";
    location:                    center;
    anchor:                      center;
    fullscreen:                  false;
    width:                       600px;
    x-offset:                    0px;
    y-offset:                    0px;
    margin:                      0px;
    padding:                     0px;
    border:                      0px solid;
    border-radius:               30px;
    border-color:                @selected;
    cursor:                      "default";
    background-color:            @background;
}

/*****----- Main Box -----*****/
mainbox {
    enabled:                     true;
    spacing:                     15px;
    margin:                      0px;
    padding:                     30px;
    background-color:            transparent;
    orientation:                 horizontal;
    children:                    [ "imagebox", "listview" ];
}

/*****----- Imagebox -----*****/
imagebox {
    border:                      2px solid;
    border-radius:               100%;
    border-color:                @selected;
    background-color:            transparent;
    background-image:            url("~/.config/rofi/images/g.png", height);
    children:                    [ "dummy", "inputbar", "dummy" ];
}

/*****----- Inputbar -----*****/
inputbar {
    enabled:                     true;
    spacing:                     15px;
    background-color:            transparent;
    text-color:                  @foreground;
    children:                    [ "dummy", "textbox-prompt-colon", "prompt", "dummy"];
}

dummy{
    background-color:            transparent;
}
textbox-prompt-colon {
    enabled:                     true;
    expand:                      false;
    str:                         "";
    padding:                     10px 13px;
    border:                      2px solid;
    border-radius:               100%;
    border-color:                @selected;
    background-color:            transparent;
    text-color:                  @foreground;
}
prompt {
    enabled:                     true;
    padding:                     10px;
    border:                      2px solid;
    border-radius:               100%;
    border-color:                @foreground;
    background-color:            @foreground;
    text-color:                  @background;
}

/*****----- Message -----*****/
message {
    enabled:                     true;
    margin:                      0px;
    padding:                     10px;
    border:                      0px solid;
    border-radius:               0px;
    border-color:                @selected;
    background-color:            @background-alt;
    text-color:                  @foreground;
}
textbox {
    background-color:            inherit;
    text-color:                  inherit;
    vertical-align:              0.5;
    horizontal-align:            0.0;
}

/*****----- Listview -----*****/
listview {
    enabled:                     true;
    columns:                     6;
    lines:                       1;
    cycle:                       true;
    scrollbar:                   false;
    layout:                      vertical;
    
    spacing:                     5px;
    background-color:            transparent;
    cursor:                      "default";
}

/*****----- Elements -----*****/
element {
    enabled:                     true;
    padding:                     11px;
    border:                      0px solid;
    border-radius:               100%;
    border-color:                @selected;
    background-color:            transparent;
    text-color:                  @foreground;
    cursor:                      pointer;
}
element-text {
    background-color:            transparent;
    text-color:                  inherit;
    cursor:                      inherit;
    vertical-align:              0.5;
    horizontal-align:            0.0;
}

element normal.normal,
element alternate.normal {
    background-color:            var(background);
    text-color:                  var(foreground);
}
element normal.urgent,
element alternate.urgent,
element selected.active {
    background-color:            var(urgent);
    text-color:                  var(background);
}
element normal.active,
element alternate.active,
element selected.urgent {
    background-color:            var(active);
    text-color:                  var(background);
}
element selected.normal {
    border:                      2px solid;
    border-radius:               100%;
    border-color:                @selected;
    background-color:            transparent;
    text-color:                  var(foreground);
}
