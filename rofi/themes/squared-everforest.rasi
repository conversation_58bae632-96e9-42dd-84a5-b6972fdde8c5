/*******************************************************************************
 * RO<PERSON> SQUARED THEME USING THE EVERFOREST PALETTE 
 * User                 : LR-Tech               
 * Theme Repo           : https://github.com/lr-tech/rofi-themes-collection
 *******************************************************************************/

* {
    font:   "FiraCode Nerd Font Medium 12";

    bg0:     #2B3339;
    bg1:     #323D43;
    fg0:     #D3C6AA;

    accent-color:     #A7C080;
    urgent-color:     #DBBC7F;

    background-color:   transparent;
    text-color:         @fg0;

    margin:     0;
    padding:    0;
    spacing:    0;
}

window {
    location:   center;
    width:      480;

    background-color:   @bg0;
}

inputbar {
    spacing:    8px; 
    padding:    8px;

    background-color:   @bg1;
}

prompt, entry, element-icon, element-text {
    vertical-align: 0.5;
}

prompt {
    text-color: @accent-color;
}

textbox {
    padding:            8px;
    background-color:   @bg1;
}

listview {
    padding:    4px 0;
    lines:      8;
    columns:    1;

    fixed-height:   false;
}

element {
    padding:    8px;
    spacing:    8px;
}

element normal normal {
    text-color: @fg0;
}

element normal urgent {
    text-color: @urgent-color;
}

element normal active {
    text-color: @accent-color;
}

element selected {
    text-color: @bg0;
}

element selected normal, element selected active {
    background-color:   @accent-color;
}

element selected urgent {
    background-color:   @urgent-color;
}

element-icon {
    size:   0.8em;
}

element-text {
    text-color: inherit;
}
