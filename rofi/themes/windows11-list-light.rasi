* {
  font: "Roboto 10";

  bg0    : #ffffff80;
  bg1    : #f9f9f9bf;
  bg2    : #f7f7f7;
  bg3    : #fefefebf;
  fg0    : #1a1a1a;
  fg1    : #5f5f5f;
  accent : #005fb8;
  urgent : @accent;

  background-color : transparent;
  text-color       : @fg0;

  margin  : 0;
  padding : 0;
  spacing : 0;
}

element-icon, element-text, scrollbar {
  cursor: pointer;
}

window {
  location : south;
  width    : 600px;
  height   : 600px;
  y-offset : -4px;

  background-color : @bg1;
  border-radius    : 8px;
}

mainbox {
  padding : 24px;
  spacing : 24px;
}

inputbar {
  padding          : 8px;
  spacing          : 4px;
  children         : [ icon-search, entry ];
  border           : 0 0 2px 0 solid;
  border-color     : @accent;
  border-radius    : 2px;
  background-color : @bg0;
}

icon-search, entry, element-icon, element-text {
  vertical-align: 0.5;
}

icon-search {
  expand   : false;
  filename : "search-symbolic";
  size     : 24px;
}

entry {
  font              : "Roboto 12";
  placeholder       : "Type here to search";
  placeholder-color : @fg1;
}

textbox {
  padding          : 4px 8px;
  background-color : @bg2;
}

listview {
  columns       : 2;
  spacing       : 8px;
  fixed-height  : true;
  fixed-columns : true;
}

element {
  spacing       : 1em;
  padding       : 8px;
  border-radius : 2px;
}

element normal urgent {
  text-color: @urgent;
}

element normal active {
  text-color: @accent;
}

element selected {
  background-color: @bg3;
}

element selected urgent {
  background-color: @urgent;
}

element-icon {
  size: 1.5em;
}

element-text {
  text-color: inherit;
}
