#!/usr/bin/env bash

## Copyright (C) 2020-2021 <PERSON><PERSON><PERSON> <<EMAIL>>
## Everyone is permitted to copy and distribute copies of this file under GNU-GPL3

DIR="$HOME/.config/hypr"

rofi_command="rofi -theme $DIR/rofi/themes/powermenu.rasi"

uptime=$(uptime -p | sed -e 's/up //g')

# Options
shutdown=""
reboot=""
lock=""
suspend=""
logout=""

# Variable passed to rofi
options="$shutdown\n$reboot\n$lock\n$suspend\n$logout"
_msg="Options  -  yes / y / no / n"

chosen="$(echo -e "$options" | $rofi_command -p "UP - $uptime" -dmenu )"
case $chosen in
    $shutdown)
		ans=$($HOME/.config/hypr/rofi/scripts/utilities/confirm)
		if [[ $ans == "yes" ]] || [[ $ans == "YES" ]] || [[ $ans == "y" ]]; then
        systemctl poweroff
		elif [[ $ans == "no" ]] || [[ $ans == "NO" ]] || [[ $ans == "n" ]]; then
        exit
        else
        rofi -theme $HOME/.config/hypr/rofi/themes/askpass.rasi -e "$_msg"
        fi
        ;;
    $reboot)
		ans=$($HOME/.config/hypr/rofi/scripts/utilities/confirm)
		if [[ $ans == "yes" ]] || [[ $ans == "YES" ]] || [[ $ans == "y" ]]; then
        systemctl reboot
		elif [[ $ans == "no" ]] || [[ $ans == "NO" ]] || [[ $ans == "n" ]]; then
        exit
        else
        rofi -theme $HOME/.config/hypr/rofi/themes/askpass.rasi -e "$_msg"
        fi
        ;;
    $lock)
        bsplock
        ;;
    $suspend)
		ans=$($HOME/.config/hypr/rofi/scripts/utilities/confirm)
		if [[ $ans == "yes" ]] || [[ $ans == "YES" ]] || [[ $ans == "y" ]]; then
        mpc -q pause
        #amixer set Master mute
        # betterlockscreen --suspend
        betterlockscreen -l dim
		elif [[ $ans == "no" ]] || [[ $ans == "NO" ]] || [[ $ans == "n" ]]; then
        exit
        else
        rofi -theme $HOME/.config/hypr/rofi/themes/askpass.rasi -e "$_msg"
        fi
        ;;
    $logout)
		ans=$($HOME/.config/hypr/rofi/scripts/utilities/confirm)
		if [[ $ans == "yes" ]] || [[ $ans == "YES" ]] || [[ $ans == "y" ]]; then
        hyprctl dispatch exit
		elif [[ $ans == "no" ]] || [[ $ans == "NO" ]] || [[ $ans == "n" ]]; then
        exit
        else
        rofi -theme $HOME/.config/hypr/rofi/themes/askpass.rasi -e "$_msg"
        fi
        ;;
esac

