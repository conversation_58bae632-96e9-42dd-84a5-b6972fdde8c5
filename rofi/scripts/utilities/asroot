#!/usr/bin/env bash

## Copyright (C) 2020-2021 <PERSON><PERSON><PERSON> <<EMAIL>>
## Everyone is permitted to copy and distribute copies of this file under GNU-GPL3

DIR="$HOME/.config/hypr"

rofi_command="rofi -theme $DIR/rofi/themes/asroot.rasi"

# Apps
terminal=" Alacritty"
files=" Pcmanfm"
editor=" Geany"
clifm=" Ranger"
lpad=" Leafpad"
vim=" Vim"

# Variable passed to rofi
options="$terminal\n$files\n$editor\n$clifm\n$lpad\n$vim"

# Functionj

chosen="$(echo -e "$options" | $rofi_command -p "Run apps as root" -dmenu )"
case $chosen in
    $terminal)
        bsproot 'alacritty --class alacritty-float,alacritty-float --config-file /root/.config/bspwm/alacritty/alacritty.yml'
        ;;
    $files)
        bsproot 'dbus-run-session pcmanfm'
        ;;
    $editor)
        bsproot geany
        ;;
    $clifm)
        bsproot 'alacritty --class alacritty-float,alacritty-float --config-file /root/.config/bspwm/alacritty/alacritty.yml -e ranger'
        ;;
    $lpad)
        bsproot leafpad
        ;;
    $vim)
        bsproot 'alacritty --class alacritty-float,alacritty-float --config-file /root/.config/bspwm/alacritty/alacritty.yml -e vim'
        ;;
esac


