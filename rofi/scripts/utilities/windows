#!/usr/bin/env bash

## Derechos de autor (C) 2020-2021 <PERSON><PERSON><PERSON> <<EMAIL>>
## Se permite a todos copiar y distribuir copias de este archivo bajo GNU-GPL3

DIR="$HOME/.config/hypr"

# Captura el número de ventanas y calcula el ancho dinámicamente
num_windows=$(wmctrl -l | wc -l)
width=$((num_windows * 150))

# Configura rofi para emular el comportamiento de Alt+Tab de Windows
rofi -modi window -show window -kb-row-down "Alt+Tab,Down" -kb-row-up "Alt+Shift+Tab,Up" -theme $DIR/rofi/themes/window.rasi -theme-str "window { width: ${width}px; } listview { columns: $num_windows; }"
