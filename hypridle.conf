general {
    lock_cmd = pidof hyprlock || hyprlock --immediate
    before_sleep_cmd = hyprlock --immediate
    after_sleep_cmd = hyprctl dispatch dpms on
}

# Screen lock after 10 minutes of inactivity
listener {
    timeout = 600
    on-timeout = hyprlock --immediate
}

# Turn off screen after 15 minutes
listener {
    timeout = 900
    on-timeout = hyprctl dispatch dpms off
    on-resume = hyprctl dispatch dpms on
}

# Suspend after 30 minutes (laptop only)
listener {
    timeout = 1800
    on-timeout = systemctl suspend
}
