# Bloquear la pantalla con hyprlock al cerrar la tapa
bindl=,switch:on:Lid Switch,exec,hyprlock --immediate && systemctl suspend

# █▀▀ █▀█ █▀█ █▀▀ █▀ █▀▀ █▀█ █▀█ █▀ █▀▀ █▀
# █▄▄ █▄█ █▀▄ ██▄ ▄█ █▄▄ █▀▄ █▄█ ▄█ ██▄ ▄█

# Core Variables
# Modifier keys
$mainMod = SUPER
$altMod = ALT
$ctrlMod = CTRL
$shiftMod = SHIFT

# Scripts and utilities
#$screenshot = ~/.config/hypr/scripts/screenshot.sh
#$colorpicker = ~/.config/hypr/scripts/colorpicker.sh
#$volume = ~/.config/hypr/scripts/volume.sh
#$brightness = ~/.config/hypr/scripts/brightness.sh

# Paths
$wallpapers = ~/Wallpapers
$screenshots = ~/Pictures/Screenshots

# Display
$gaps = 4
$border = 2
$radius = 8

# █▀ █▀█ █░█ █▀█ █▀▀ █▀▀ █▀
# ▄█ █▄█ █▄█ █▀▄ █▄▄ ██▄ ▄█

# Configuration Files
# Load configuration files
source = config/window-rules.conf
source = config/monitors.conf
source = config/keybinds.conf
source = config/autostart.conf
source = config/environment.conf
source = config/input.conf
# Load theme
source = themes/colors/default.conf



# █▀▀ █▀▀ █▄░█ █▀▀ █▀█ ▄▀█ █░░
# █▄█ ██▄ █░▀█ ██▄ █▀▄ █▀█ █▄▄

general {
    # Gaps and Borders
    gaps_in = 4
    gaps_out = 4
    border_size = 0
    col.active_border = rgba(33ccffee) rgba(00ff99ee) 45deg
    col.inactive_border = rgba(595959aa)

    # Layout
    layout = dwindle

    # Display
    allow_tearing = false
}

# █▀▄ █▀▀ █▀▀ █▀█ █▀█ ▄▀█ ▀█▀ █ █▀█ █▄░█
# █▄▀ ██▄ █▄▄ █▄█ █▀▄ █▀█ ░█░ █ █▄█ █░▀█

decoration {
    rounding = 16

    # Blur Effect
    blur {
        enabled = true
        size = 8
        passes = 2
        new_optimizations = true
        ignore_opacity = true
    }

    # Shadow Effect
    #drop_shadow = true
    #shadow_range = 8
    #shadow_render_power = 2
    #col.shadow = rgba(00000044)
    #shadow_offset = 2 2

    # Active/Inactive
    active_opacity = 1.0
    inactive_opacity = 1

    # Window Dimming
    dim_inactive = false
    dim_strength = 0.1
}

# █▀▀ █▀▀ █▀▀ █▀▀ █▀▀ ▀█▀ █▀
# ██▄ █▀░ █▀░ ██▄ █▄▄ ░█░ ▄█

animations {
    enabled = true

    # Bezier Curves
    bezier = linear, 0, 0, 1, 1
    bezier = md3_standard, 0.2, 0, 0, 1
    bezier = md3_decel, 0.05, 0.7, 0.1, 1
    bezier = md3_accel, 0.3, 0, 0.8, 0.15
    bezier = overshot, 0.05, 0.9, 0.1, 1.1
    bezier = crazyshot, 0.1, 1.5, 0.76, 0.92
    bezier = hyprnostretch, 0.05, 0.9, 0.1, 1.0
    bezier = menu_decel, 0.1, 1, 0, 1
    bezier = menu_accel, 0.38, 0.04, 1, 0.07
    bezier = easeInOutCirc, 0.85, 0, 0.15, 1
    bezier = easeOutCirc, 0, 0.55, 0.45, 1
    bezier = easeOutExpo, 0.16, 1, 0.3, 1
    bezier = softAcDecel, 0.26, 0.26, 0.15, 1
    bezier = md2, 0.4, 0, 0.2, 1

    # Window Animations
    animation = windows, 1, 3, md3_decel, popin 60%
    animation = windowsIn, 1, 3, md3_decel, popin 60%
    animation = windowsOut, 1, 3, md3_accel, popin 60%
    animation = border, 1, 10, default
    animation = fade, 1, 3, md3_decel

    # Workspace Animations
    animation = workspaces, 1, 7, menu_decel, slide
    animation = specialWorkspace, 1, 3, md3_decel, slidevert
}

dwindle {
    # See https://wiki.hyprland.org/Configuring/Dwindle-Layout/ for more
    pseudotile = true # master switch for pseudotiling. Enabling is bound to mainMod + P in the keybinds section below
    preserve_split = true # you probably want this
}
master {
    new_on_top = true
}

# █▀▀ █▀▀ █▀ ▀█▀ █░█ █▀█ █▀▀ █▀
# █▄█ ██▄ ▄█ ░█░ █▄█ █▀▄ ██▄ ▄█

gestures {
    # Touchpad workspace swipe
    workspace_swipe = true
    workspace_swipe_fingers = 3
    workspace_swipe_distance = 300
    workspace_swipe_invert = true
    workspace_swipe_min_speed_to_force = 30
    workspace_swipe_cancel_ratio = 0.5
    workspace_swipe_create_new = false

    # Touchpad window actions
    workspace_swipe_direction_lock = true
    workspace_swipe_direction_lock_threshold = 10

    # Touch screen
    workspace_swipe_use_r = true
}



# Configuración de monitor y workspaces

# █▀▄▀█ █ █▀ █▀▀
# █░▀░█ █ ▄█ █▄▄

misc {
    font_family = Cantarell
    disable_hyprland_logo = 1
    vfr = true
}

# Example per-device config
# See https://wiki.hyprland.org/Configuring/Keywords/#per-device-input-configs for more
device {
    name = epic-mouse-v1
    sensitivity = 0.5

}
