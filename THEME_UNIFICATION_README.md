# Unificación de Temas Qt/GTK en Hyprland

## Problema Resuelto

Tenías temas inconsistentes:
- **Qt**: Fusion theme + candy-icons
- **GTK**: Adwaita-dark + Adwaita icons

Esto causaba que las aplicaciones se vieran diferentes dependiendo de si usaban Qt o GTK.

## Solución Implementada

### ✅ **Configuración Unificada**

**Tema**: `Adwaita-dark` (tanto Qt como GTK)
**Iconos**: `Adwaita` (tanto Qt como GTK)  
**Cursor**: `Sweet-cursors` (consistente en todo)

### 🔧 **Cambios en Variables de Entorno**

En `config/environment.conf`:
```bash
# Qt/GTK Theme Configuration
env = QT_QPA_PLATFORMTHEME,gtk3        # Qt sigue a GTK
env = QT_STYLE_OVERRIDE,Adwaita-Dark   # Fuerza tema Qt
env = GTK_THEME,Adwaita:dark           # Tema GTK oscuro

# Cursor unificado
env = HYPRCURSOR_THEME,Sweet-cursors
env = XCURSOR_THEME,Sweet-cursors
env = QT_CURSOR_THEME,Sweet-cursors
```

### 📝 **Script theme-unifier.sh Creado**

**Ubicación**: `~/.config/hypr/scripts/system/theme-unifier.sh`

**Funciones**:
- `apply` - Aplica configuración unificada
- `status` - Muestra estado actual de temas
- `reset` - Resetea a temas por defecto

**Configuraciones que crea**:
- GTK2/3/4 settings
- Qt5ct/Qt6ct configuration
- gsettings aplicados
- Symlinks de cursor

### 🚀 **Autostart Configurado**

En `config/autostart.conf`:
```bash
# Theme unification (cursor + Qt/GTK themes)
exec-once = ~/.config/hypr/scripts/system/theme-unifier.sh apply &
```

### ⌨️ **Keybind Agregado**

En `config/keybinds.conf`:
```bash
# Theme controls
bind = $mainMod SHIFT, T, exec, ~/.config/hypr/scripts/system/theme-unifier.sh apply && notify-send "Themes unified"
```

## Uso

### **Aplicar Temas Unificados**
```bash
# Presiona Super + Shift + T
# O ejecuta:
./scripts/system/theme-unifier.sh apply
```

### **Ver Estado de Temas**
```bash
./scripts/system/theme-unifier.sh status
```

### **Resetear Temas**
```bash
./scripts/system/theme-unifier.sh reset
```

## Resultado

Ahora tendrás **apariencia consistente** en:

✅ **Aplicaciones Qt** (como VLC, qBittorrent, etc.)
✅ **Aplicaciones GTK** (como Firefox, Nautilus, etc.)
✅ **Iconos unificados** (Adwaita en todas las apps)
✅ **Cursor consistente** (Sweet-cursors en todo)
✅ **Tema oscuro** (Adwaita-dark en todas las apps)

## Antes vs Después

**Antes**:
```
Theme: Fusion [Qt], Adwaita-dark [GTK2/3/4]
Icons: candy-icons [Qt], Adwaita [GTK2/3/4]
```

**Después**:
```
Theme: Adwaita-dark [Qt], Adwaita-dark [GTK2/3/4]
Icons: Adwaita [Qt], Adwaita [GTK2/3/4]
Cursor: Sweet-cursors [All]
```

## Para Aplicar Completamente

**Reinicia Hyprland** para que las variables de entorno tomen efecto:
```bash
hyprctl dispatch exit
```

O reinicia aplicaciones individuales para ver los cambios inmediatamente.

## Archivos Creados/Modificados

- ✅ `config/environment.conf` - Variables de entorno Qt/GTK
- ✅ `config/autostart.conf` - Autostart del unificador
- ✅ `config/keybinds.conf` - Keybind para temas
- ✅ `scripts/system/theme-unifier.sh` - Script principal
- ✅ `~/.config/gtk-3.0/settings.ini` - Configuración GTK3
- ✅ `~/.config/gtk-4.0/settings.ini` - Configuración GTK4
- ✅ `~/.gtkrc-2.0` - Configuración GTK2
- ✅ `~/.config/qt5ct/qt5ct.conf` - Configuración Qt5
- ✅ `~/.config/qt6ct/qt6ct.conf` - Configuración Qt6

## Troubleshooting

### Si algunas aplicaciones Qt no siguen el tema:
```bash
# Ejecuta el unificador manualmente
./scripts/system/theme-unifier.sh apply

# O presiona Super + Shift + T
```

### Si necesitas cambiar el tema base:
Edita las variables en `scripts/system/theme-unifier.sh`:
```bash
GTK_THEME="Adwaita-dark"    # Cambia aquí
ICON_THEME="Adwaita"        # Cambia aquí
CURSOR_THEME="Sweet-cursors" # Cambia aquí
```

¡Ahora tendrás una apariencia completamente unificada en todo el sistema! 🎨✨
