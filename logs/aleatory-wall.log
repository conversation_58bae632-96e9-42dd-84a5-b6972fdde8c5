[2025-07-24 10:06:58] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:06:59] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:06:59] === AleatoryWall Script Started ===
[2025-07-24 10:06:59] [ERROR] Missing dependencies: wal
[2025-07-24 10:06:59] [ERROR] Please install the missing packages
[2025-07-24 10:10:25] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:10:26] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:10:26] === AleatoryWall Script Started ===
[2025-07-24 10:10:26] [ERROR] Missing dependencies: wal
[2025-07-24 10:10:26] [ERROR] Please install the missing packages
[2025-07-24 10:10:57] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:10:58] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:10:58] === AleatoryWall Script Started ===
[2025-07-24 10:10:58] [ERROR] Missing dependencies: wal
[2025-07-24 10:10:58] [ERROR] Please install the missing packages
[2025-07-24 10:52:15] [DEBUG] Searching for wallpaper directories...
[2025-07-24 10:52:15] [DEBUG] Checking directory: /home/<USER>/Wallpapers
[2025-07-24 10:52:15] [DEBUG] Directory exists: /home/<USER>/Wallpapers
[2025-07-24 10:52:15] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:52:15] [DEBUG] Using wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 10:52:15] [DEBUG] Checking for previous instances of AleatoryWall.sh
[2025-07-24 10:52:15] [DEBUG] Killing previous instance with PID: 8340
[2025-07-24 10:52:16] [DEBUG] Searching for image files in /home/<USER>/Wallpapers
[2025-07-24 10:52:16] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:52:16] === AleatoryWall Script Started ===
[2025-07-24 10:52:16] [ERROR] Missing dependencies: wal
[2025-07-24 10:52:16] [ERROR] Please install the missing packages
[2025-07-24 10:53:08] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:53:09] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:53:09] === AleatoryWall Script Started ===
[2025-07-24 10:53:09] Running in single-change mode
[2025-07-24 10:53:09] Changing wallpaper to: WALL12.jpg
[2025-07-24 10:53:48] Script terminating, cleaning up...
[2025-07-24 10:54:04] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:54:05] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:54:05] === AleatoryWall Script Started ===
[2025-07-24 10:54:05] Running in single-change mode
[2025-07-24 10:54:05] Changing wallpaper to: WALL10.png
[2025-07-24 10:55:07] Script terminating, cleaning up...
[2025-07-24 10:55:19] [DEBUG] Searching for wallpaper directories...
[2025-07-24 10:55:19] [DEBUG] Checking directory: /home/<USER>/Wallpapers
[2025-07-24 10:55:19] [DEBUG] Directory exists: /home/<USER>/Wallpapers
[2025-07-24 10:55:19] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:55:19] [DEBUG] Using wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 10:55:19] [DEBUG] Checking for previous instances of AleatoryWall.sh
[2025-07-24 10:55:19] [DEBUG] Killing previous instance with PID: 9539
[2025-07-24 10:55:20] [DEBUG] Searching for image files in /home/<USER>/Wallpapers
[2025-07-24 10:55:20] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:55:20] === AleatoryWall Script Started ===
[2025-07-24 10:55:20] [DEBUG] Optional dependency not found: wal (features may be limited)
[2025-07-24 10:55:20] [DEBUG] All required dependencies are available
[2025-07-24 10:55:20] Running in single-change mode
[2025-07-24 10:55:20] [DEBUG] Starting wallpaper change process
[2025-07-24 10:55:20] Changing wallpaper to: WALL12.jpg
[2025-07-24 10:55:20] [DEBUG] Checking if swww daemon is running
[2025-07-24 10:55:20] [DEBUG] swww daemon already running
[2025-07-24 10:55:20] [DEBUG] Setting wallpaper with swww
[2025-07-24 10:55:21] [DEBUG] swww wallpaper set successfully
[2025-07-24 10:55:21] [DEBUG] pywal not available, skipping color generation
[2025-07-24 10:55:21] [DEBUG] Restarting SwayNC
[2025-07-24 11:22:06] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:22:07] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 11:22:07] === AleatoryWall Script Started ===
[2025-07-24 11:22:07] AleatoryWall daemon started in background
[2025-07-24 11:22:07] Starting AleatoryWall daemon
[2025-07-24 11:22:07] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 11:22:07] Found 14 wallpapers
[2025-07-24 11:22:07] Change interval: 60 minutes
[2025-07-24 11:22:07] Changing wallpaper to: WALL3(1).jpg
