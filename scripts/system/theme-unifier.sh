#!/bin/bash
# Theme Unifier Script
# Unifies Qt and GTK themes for consistent appearance

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
GTK_THEME="Sweet-Dark-v40"
ICON_THEME="candy-icons"
CURSOR_THEME="Sweet-cursors"
CURSOR_SIZE="24"

# Directories
GTK3_CONFIG="$HOME/.config/gtk-3.0"
GTK4_CONFIG="$HOME/.config/gtk-4.0"
QT5CT_CONFIG="$HOME/.config/qt5ct"
QT6CT_CONFIG="$HOME/.config/qt6ct"
GTKRC2="$HOME/.gtkrc-2.0"

show_help() {
    echo -e "${CYAN}Theme Unifier Script${NC}"
    echo "===================="
    echo
    echo -e "${YELLOW}Usage:${NC} $0 [OPTION]"
    echo
    echo -e "${YELLOW}Options:${NC}"
    echo -e "  ${GREEN}apply${NC}     Apply unified theme configuration (default)"
    echo -e "  ${GREEN}status${NC}    Show current theme status"
    echo -e "  ${GREEN}reset${NC}     Reset to default themes"
    echo -e "  ${GREEN}help${NC}      Show this help"
    echo
    echo -e "${YELLOW}What this script does:${NC}"
    echo "• Unifies Qt and GTK themes to use Adwaita-dark"
    echo "• Sets consistent icon theme (Adwaita)"
    echo "• Ensures cursor theme consistency (Sweet-cursors)"
    echo "• Configures Qt to follow GTK theme"
    echo
}

create_gtk_configs() {
    echo -e "${BLUE}📝 Creating GTK configuration files...${NC}"
    
    # Create GTK3 config directory
    mkdir -p "$GTK3_CONFIG"
    
    # Create GTK3 settings.ini
    cat > "$GTK3_CONFIG/settings.ini" << EOF
[Settings]
gtk-theme-name=$GTK_THEME
gtk-icon-theme-name=$ICON_THEME
gtk-cursor-theme-name=$CURSOR_THEME
gtk-cursor-theme-size=$CURSOR_SIZE
gtk-font-name=Cantarell 11
gtk-application-prefer-dark-theme=true
gtk-decoration-layout=:minimize,maximize,close
gtk-enable-animations=true
gtk-primary-button-warps-slider=false
EOF
    
    # Create GTK4 config directory
    mkdir -p "$GTK4_CONFIG"
    
    # Create GTK4 settings.ini
    cat > "$GTK4_CONFIG/settings.ini" << EOF
[Settings]
gtk-theme-name=$GTK_THEME
gtk-icon-theme-name=$ICON_THEME
gtk-cursor-theme-name=$CURSOR_THEME
gtk-cursor-theme-size=$CURSOR_SIZE
gtk-font-name=Cantarell 11
gtk-application-prefer-dark-theme=true
gtk-decoration-layout=:minimize,maximize,close
gtk-enable-animations=true
gtk-primary-button-warps-slider=false
EOF
    
    # Create GTK2 config
    cat > "$GTKRC2" << EOF
gtk-theme-name="$GTK_THEME"
gtk-icon-theme-name="$ICON_THEME"
gtk-cursor-theme-name="$CURSOR_THEME"
gtk-cursor-theme-size=$CURSOR_SIZE
gtk-font-name="Cantarell 11"
EOF
    
    echo -e "${GREEN}✅ GTK configuration files created${NC}"
}

create_qt_configs() {
    echo -e "${BLUE}📝 Creating Qt configuration files...${NC}"
    
    # Create Qt5ct config directory
    mkdir -p "$QT5CT_CONFIG"
    
    # Create Qt5ct config
    cat > "$QT5CT_CONFIG/qt5ct.conf" << EOF
[Appearance]
color_scheme_path=
custom_palette=false
icon_theme=$ICON_THEME
standard_dialogs=gtk3
style=gtk2

[Fonts]
fixed=@Variant(\0\0\0@\0\0\0\x12\0M\0o\0n\0o\0s\0p\0\x61\0\x63\0\x65@\"\0\0\0\0\0\0\xff\xff\xff\xff\x5\x1\0\x32\x10)
general=@Variant(\0\0\0@\0\0\0\x16\0\x43\0\x61\0n\0t\0\x61\0r\0\x65\0l\0l@\"\0\0\0\0\0\0\xff\xff\xff\xff\x5\x1\0\x32\x10)

[Interface]
activate_item_on_single_click=1
buttonbox_layout=3
cursor_flash_time=1000
dialog_buttons_have_icons=2
double_click_interval=400
gui_effects=@Invalid()
keyboard_scheme=2
menus_have_icons=true
show_shortcuts_in_context_menus=true
stylesheets=@Invalid()
toolbutton_style=4
underline_shortcut=1
wheel_scroll_lines=3

[SettingsWindow]
geometry=@ByteArray()

[Troubleshooting]
force_raster_widgets=1
ignored_applications=@Invalid()
EOF
    
    # Create Qt6ct config directory
    mkdir -p "$QT6CT_CONFIG"
    
    # Create Qt6ct config (similar to Qt5ct)
    cat > "$QT6CT_CONFIG/qt6ct.conf" << EOF
[Appearance]
color_scheme_path=
custom_palette=false
icon_theme=$ICON_THEME
standard_dialogs=gtk3
style=gtk2

[Fonts]
fixed=@Variant(\0\0\0@\0\0\0\x12\0M\0o\0n\0o\0s\0p\0\x61\0\x63\0\x65@\"\0\0\0\0\0\0\xff\xff\xff\xff\x5\x1\0\x32\x10)
general=@Variant(\0\0\0@\0\0\0\x16\0\x43\0\x61\0n\0t\0\x61\0r\0\x65\0l\0l@\"\0\0\0\0\0\0\xff\xff\xff\xff\x5\x1\0\x32\x10)

[Interface]
activate_item_on_single_click=1
buttonbox_layout=3
cursor_flash_time=1000
dialog_buttons_have_icons=2
double_click_interval=400
gui_effects=@Invalid()
keyboard_scheme=2
menus_have_icons=true
show_shortcuts_in_context_menus=true
stylesheets=@Invalid()
toolbutton_style=4
underline_shortcut=1
wheel_scroll_lines=3

[SettingsWindow]
geometry=@ByteArray()

[Troubleshooting]
force_raster_widgets=1
ignored_applications=@Invalid()
EOF
    
    echo -e "${GREEN}✅ Qt configuration files created${NC}"
}

apply_gsettings() {
    echo -e "${BLUE}🔄 Applying GTK settings via gsettings...${NC}"
    
    if command -v gsettings >/dev/null 2>&1; then
        # GTK theme settings
        gsettings set org.gnome.desktop.interface gtk-theme "$GTK_THEME"
        gsettings set org.gnome.desktop.interface icon-theme "$ICON_THEME"
        gsettings set org.gnome.desktop.interface cursor-theme "$CURSOR_THEME"
        gsettings set org.gnome.desktop.interface cursor-size "$CURSOR_SIZE"
        
        # Font settings
        gsettings set org.gnome.desktop.interface font-name "Cantarell 11"
        gsettings set org.gnome.desktop.interface monospace-font-name "Monospace 10"
        
        # Appearance settings
        gsettings set org.gnome.desktop.interface color-scheme 'prefer-dark'
        gsettings set org.gnome.desktop.interface enable-animations true
        
        echo -e "${GREEN}✅ GTK settings applied via gsettings${NC}"
    else
        echo -e "${YELLOW}⚠️  gsettings not available${NC}"
    fi
}

apply_unified_theme() {
    echo -e "${CYAN}🎨 Applying Unified Theme Configuration${NC}"
    echo "======================================"
    echo
    echo -e "${BLUE}Target Configuration:${NC}"
    echo -e "  Theme: ${GREEN}$GTK_THEME${NC} (both Qt and GTK)"
    echo -e "  Icons: ${GREEN}$ICON_THEME${NC} (both Qt and GTK)"
    echo -e "  Cursor: ${GREEN}$CURSOR_THEME${NC}"
    echo
    
    # Create configuration files
    create_gtk_configs
    create_qt_configs
    apply_gsettings
    
    # Set environment variables for current session
    export GTK_THEME="Sweet-Dark-v40"
    export QT_STYLE_OVERRIDE="Adwaita-Dark"
    
    echo
    echo -e "${GREEN}✅ Unified theme configuration applied!${NC}"
    echo
    echo -e "${YELLOW}📋 Next steps:${NC}"
    echo -e "  1. Restart applications to see changes"
    echo -e "  2. Or restart Hyprland: ${CYAN}hyprctl dispatch exit${NC}"
    echo -e "  3. Qt apps will now follow GTK theme"
    echo
}

show_theme_status() {
    echo -e "${CYAN}🎨 Current Theme Status${NC}"
    echo "======================="
    echo
    
    # Environment variables
    echo -e "${YELLOW}Environment Variables:${NC}"
    echo -e "  GTK_THEME: ${GREEN}${GTK_THEME:-not set}${NC}"
    echo -e "  QT_STYLE_OVERRIDE: ${GREEN}${QT_STYLE_OVERRIDE:-not set}${NC}"
    echo -e "  QT_QPA_PLATFORMTHEME: ${GREEN}${QT_QPA_PLATFORMTHEME:-not set}${NC}"
    echo
    
    # GTK settings
    echo -e "${YELLOW}GTK Settings (gsettings):${NC}"
    if command -v gsettings >/dev/null 2>&1; then
        local gtk_theme=$(gsettings get org.gnome.desktop.interface gtk-theme 2>/dev/null | tr -d "'")
        local icon_theme=$(gsettings get org.gnome.desktop.interface icon-theme 2>/dev/null | tr -d "'")
        local cursor_theme=$(gsettings get org.gnome.desktop.interface cursor-theme 2>/dev/null | tr -d "'")
        
        echo -e "  GTK Theme: ${GREEN}$gtk_theme${NC}"
        echo -e "  Icon Theme: ${GREEN}$icon_theme${NC}"
        echo -e "  Cursor Theme: ${GREEN}$cursor_theme${NC}"
    else
        echo -e "  ${YELLOW}⚠️  gsettings not available${NC}"
    fi
    echo
    
    # Configuration files
    echo -e "${YELLOW}Configuration Files:${NC}"
    local configs=(
        "$GTK3_CONFIG/settings.ini:GTK3"
        "$GTK4_CONFIG/settings.ini:GTK4"
        "$GTKRC2:GTK2"
        "$QT5CT_CONFIG/qt5ct.conf:Qt5ct"
        "$QT6CT_CONFIG/qt6ct.conf:Qt6ct"
    )
    
    for config in "${configs[@]}"; do
        local file="${config%:*}"
        local name="${config#*:}"
        
        if [[ -f "$file" ]]; then
            echo -e "  ${GREEN}✅${NC} $name config exists"
        else
            echo -e "  ${RED}❌${NC} $name config missing"
        fi
    done
    echo
}

reset_themes() {
    echo -e "${BLUE}🔄 Resetting to default themes...${NC}"
    
    # Remove custom configurations
    rm -f "$GTK3_CONFIG/settings.ini" 2>/dev/null
    rm -f "$GTK4_CONFIG/settings.ini" 2>/dev/null
    rm -f "$GTKRC2" 2>/dev/null
    rm -f "$QT5CT_CONFIG/qt5ct.conf" 2>/dev/null
    rm -f "$QT6CT_CONFIG/qt6ct.conf" 2>/dev/null
    
    # Reset gsettings
    if command -v gsettings >/dev/null 2>&1; then
        gsettings reset org.gnome.desktop.interface gtk-theme
        gsettings reset org.gnome.desktop.interface icon-theme
        gsettings reset org.gnome.desktop.interface cursor-theme
    fi
    
    echo -e "${GREEN}✅ Themes reset to defaults${NC}"
}

# Main execution
case "${1:-apply}" in
    apply)
        apply_unified_theme
        ;;
    status)
        show_theme_status
        ;;
    reset)
        reset_themes
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}❌ Unknown option: $1${NC}"
        echo
        show_help
        exit 1
        ;;
esac
