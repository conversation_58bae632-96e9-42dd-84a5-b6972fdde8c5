#!/bin/bash
# Cursor Theme Setup Script
# Configures consistent cursor theme across all applications

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
CURSOR_THEME="Sweet-cursors"
CURSOR_SIZE="24"
GTK_THEME="Adwaita-dark"
ICON_THEME="Adwaita"

# Directories
GTK3_CONFIG="$HOME/.config/gtk-3.0"
GTK4_CONFIG="$HOME/.config/gtk-4.0"
GTKRC2="$HOME/.gtkrc-2.0"

show_help() {
    echo -e "${CYAN}Cursor Theme Setup Script${NC}"
    echo "========================="
    echo
    echo -e "${YELLOW}Usage:${NC} $0 [OPTION] [THEME]"
    echo
    echo -e "${YELLOW}Options:${NC}"
    echo -e "  ${GREEN}apply${NC}     Apply cursor theme (default: oreo_black_cursors)"
    echo -e "  ${GREEN}list${NC}      List available cursor themes"
    echo -e "  ${GREEN}current${NC}   Show current cursor configuration"
    echo -e "  ${GREEN}reset${NC}     Reset to default cursor"
    echo -e "  ${GREEN}help${NC}      Show this help"
    echo
    echo -e "${YELLOW}Available themes:${NC}"
    echo -e "  • oreo_black_cursors (recommended)"
    echo -e "  • Sweet-cursors"
    echo -e "  • capitaine-cursors"
    echo -e "  • capitaine-cursors-light"
    echo -e "  • Adwaita"
    echo -e "  • redglass"
    echo -e "  • whiteglass"
    echo
}

list_cursor_themes() {
    echo -e "${CYAN}📁 Available Cursor Themes:${NC}"
    echo "============================"
    echo
    
    local count=0
    
    # Check system themes
    if [[ -d "/usr/share/icons" ]]; then
        echo -e "${YELLOW}System themes (/usr/share/icons):${NC}"
        for theme_dir in /usr/share/icons/*/cursors; do
            if [[ -d "$theme_dir" ]]; then
                local theme_name=$(basename "$(dirname "$theme_dir")")
                count=$((count + 1))
                echo -e "  ${GREEN}$count.${NC} $theme_name"
            fi
        done
        echo
    fi
    
    # Check user themes
    if [[ -d "$HOME/.icons" ]]; then
        echo -e "${YELLOW}User themes (~/.icons):${NC}"
        for theme_dir in "$HOME/.icons"/*/cursors; do
            if [[ -d "$theme_dir" ]]; then
                local theme_name=$(basename "$(dirname "$theme_dir")")
                count=$((count + 1))
                echo -e "  ${GREEN}$count.${NC} $theme_name"
            fi
        done
        echo
    fi
    
    if [[ -d "$HOME/.local/share/icons" ]]; then
        echo -e "${YELLOW}Local themes (~/.local/share/icons):${NC}"
        for theme_dir in "$HOME/.local/share/icons"/*/cursors; do
            if [[ -d "$theme_dir" ]]; then
                local theme_name=$(basename "$(dirname "$theme_dir")")
                count=$((count + 1))
                echo -e "  ${GREEN}$count.${NC} $theme_name"
            fi
        done
        echo
    fi
    
    echo -e "${BLUE}📊 Total themes found: $count${NC}"
}

show_current_config() {
    echo -e "${CYAN}🖱️  Current Cursor Configuration:${NC}"
    echo "=================================="
    echo
    
    # Environment variables
    echo -e "${YELLOW}Environment Variables:${NC}"
    echo -e "  XCURSOR_THEME: ${GREEN}${XCURSOR_THEME:-not set}${NC}"
    echo -e "  XCURSOR_SIZE: ${GREEN}${XCURSOR_SIZE:-not set}${NC}"
    echo -e "  HYPRCURSOR_THEME: ${GREEN}${HYPRCURSOR_THEME:-not set}${NC}"
    echo -e "  HYPRCURSOR_SIZE: ${GREEN}${HYPRCURSOR_SIZE:-not set}${NC}"
    echo
    
    # GTK configuration
    echo -e "${YELLOW}GTK Configuration:${NC}"
    if [[ -f "$GTK3_CONFIG/settings.ini" ]]; then
        local gtk_cursor=$(grep "gtk-cursor-theme-name" "$GTK3_CONFIG/settings.ini" 2>/dev/null | cut -d'=' -f2)
        echo -e "  GTK3 cursor: ${GREEN}${gtk_cursor:-not set}${NC}"
    else
        echo -e "  GTK3 cursor: ${RED}config not found${NC}"
    fi
    
    if [[ -f "$GTKRC2" ]]; then
        local gtk2_cursor=$(grep "gtk-cursor-theme-name" "$GTKRC2" 2>/dev/null | cut -d'"' -f2)
        echo -e "  GTK2 cursor: ${GREEN}${gtk2_cursor:-not set}${NC}"
    else
        echo -e "  GTK2 cursor: ${RED}config not found${NC}"
    fi
    echo
}

create_gtk_configs() {
    echo -e "${BLUE}📝 Creating GTK configuration files...${NC}"
    
    # Create GTK3 config directory
    mkdir -p "$GTK3_CONFIG"
    
    # Create GTK3 settings.ini
    cat > "$GTK3_CONFIG/settings.ini" << EOF
[Settings]
gtk-theme-name=$GTK_THEME
gtk-icon-theme-name=$ICON_THEME
gtk-cursor-theme-name=$CURSOR_THEME
gtk-cursor-theme-size=$CURSOR_SIZE
gtk-font-name=Cantarell 11
gtk-application-prefer-dark-theme=true
EOF
    
    # Create GTK4 config directory
    mkdir -p "$GTK4_CONFIG"
    
    # Create GTK4 settings.ini
    cat > "$GTK4_CONFIG/settings.ini" << EOF
[Settings]
gtk-theme-name=$GTK_THEME
gtk-icon-theme-name=$ICON_THEME
gtk-cursor-theme-name=$CURSOR_THEME
gtk-cursor-theme-size=$CURSOR_SIZE
gtk-font-name=Cantarell 11
gtk-application-prefer-dark-theme=true
EOF
    
    # Create GTK2 config
    cat > "$GTKRC2" << EOF
gtk-theme-name="$GTK_THEME"
gtk-icon-theme-name="$ICON_THEME"
gtk-cursor-theme-name="$CURSOR_THEME"
gtk-cursor-theme-size=$CURSOR_SIZE
gtk-font-name="Cantarell 11"
EOF
    
    echo -e "${GREEN}✅ GTK configuration files created${NC}"
}

apply_cursor_theme() {
    local theme="${1:-$CURSOR_THEME}"
    
    echo -e "${BLUE}🖱️  Applying cursor theme: ${YELLOW}$theme${NC}"
    echo
    
    # Verify theme exists
    local theme_found=false
    for search_dir in "/usr/share/icons" "$HOME/.icons" "$HOME/.local/share/icons"; do
        if [[ -d "$search_dir/$theme/cursors" ]]; then
            theme_found=true
            echo -e "${GREEN}✅ Found theme at: $search_dir/$theme${NC}"
            break
        fi
    done
    
    if [[ "$theme_found" == false ]]; then
        echo -e "${RED}❌ Cursor theme '$theme' not found${NC}"
        echo -e "${YELLOW}💡 Run '$0 list' to see available themes${NC}"
        return 1
    fi
    
    # Update global variables
    CURSOR_THEME="$theme"
    
    # Create GTK configurations
    create_gtk_configs
    
    # Update Hyprland environment (requires restart)
    echo -e "${BLUE}📝 Updating Hyprland environment configuration...${NC}"
    
    # Apply cursor theme immediately for current session
    export XCURSOR_THEME="$theme"
    export XCURSOR_SIZE="$CURSOR_SIZE"
    export HYPRCURSOR_THEME="$theme"
    export HYPRCURSOR_SIZE="$CURSOR_SIZE"
    
    # Reload GTK settings
    if command -v gsettings >/dev/null 2>&1; then
        echo -e "${BLUE}🔄 Applying GTK settings...${NC}"
        gsettings set org.gnome.desktop.interface cursor-theme "$theme"
        gsettings set org.gnome.desktop.interface cursor-size "$CURSOR_SIZE"
        echo -e "${GREEN}✅ GTK settings applied${NC}"
    fi
    
    echo
    echo -e "${GREEN}✅ Cursor theme applied successfully!${NC}"
    echo
    echo -e "${YELLOW}📋 Next steps:${NC}"
    echo -e "  1. Restart Hyprland for full effect: ${CYAN}hyprctl dispatch exit${NC}"
    echo -e "  2. Or reload applications individually"
    echo -e "  3. The cursor theme is now set to: ${GREEN}$theme${NC}"
    echo
}

reset_cursor() {
    echo -e "${BLUE}🔄 Resetting cursor to default...${NC}"
    apply_cursor_theme "Adwaita"
}

# Main execution
case "${1:-apply}" in
    apply)
        apply_cursor_theme "$2"
        ;;
    list)
        list_cursor_themes
        ;;
    current)
        show_current_config
        ;;
    reset)
        reset_cursor
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}❌ Unknown option: $1${NC}"
        echo
        show_help
        exit 1
        ;;
esac
