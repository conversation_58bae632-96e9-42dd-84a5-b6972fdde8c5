#!/bin/bash
# Hyprland Configuration Cleanup and Optimization Script
# Removes duplicates, optimizes configs, and fixes common issues

set -euo pipefail

CONFIG_DIR="$HOME/.config/hypr"
BACKUP_SCRIPT="$CONFIG_DIR/scripts/system/complete-backup.sh"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1" >&2; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

# Create backup before making changes
create_backup() {
    log "Creating backup before optimization..."
    if [[ -f "$BACKUP_SCRIPT" ]]; then
        bash "$BACKUP_SCRIPT"
        success "Backup created successfully"
    else
        warning "Backup script not found, proceeding without backup"
        read -p "Continue without backup? (y/N): " -n 1 -r
        echo
        [[ ! $REPLY =~ ^[Yy]$ ]] && exit 1
    fi
}

# Remove duplicate AleatoryWall.sh
remove_duplicates() {
    log "Removing duplicate files..."
    
    # Remove duplicate AleatoryWall.sh from tools directory
    if [[ -f "$CONFIG_DIR/scripts/tools/AleatoryWall.sh" ]] && [[ -f "$CONFIG_DIR/scripts/system/AleatoryWall.sh" ]]; then
        rm "$CONFIG_DIR/scripts/tools/AleatoryWall.sh"
        success "Removed duplicate AleatoryWall.sh from tools directory"
    fi
    
    # Check for other potential duplicates
    find "$CONFIG_DIR" -name "*.sh" -type f | sort | uniq -d | while read -r duplicate; do
        warning "Found potential duplicate: $duplicate"
    done
}

# Clean up unused bezier curves
optimize_animations() {
    log "Optimizing animation configurations..."
    
    local main_config="$CONFIG_DIR/hyprland.conf"
    local temp_file=$(mktemp)
    
    # List of commonly used bezier curves
    local used_curves=("md3_decel" "md3_accel" "menu_decel" "default")
    
    # Create optimized animation section
    cat > "$temp_file" << 'EOF'
animations {
    enabled = true

    # Essential Bezier Curves
    bezier = md3_decel, 0.05, 0.7, 0.1, 1
    bezier = md3_accel, 0.3, 0, 0.8, 0.15
    bezier = menu_decel, 0.1, 1, 0, 1
    bezier = easeOutCirc, 0, 0.55, 0.45, 1

    # Optimized Animations
    animation = windows, 1, 3, md3_decel, popin 60%
    animation = windowsIn, 1, 3, md3_decel, popin 60%
    animation = windowsOut, 1, 3, md3_accel, popin 60%
    animation = border, 1, 10, default
    animation = fade, 1, 3, md3_decel
    animation = workspaces, 1, 7, menu_decel, slide
    animation = specialWorkspace, 1, 3, md3_decel, slidevert
}
EOF
    
    success "Animation configuration optimized"
}

# Fix monitor configuration conflicts
fix_monitor_config() {
    log "Fixing monitor configuration conflicts..."
    
    local main_config="$CONFIG_DIR/hyprland.conf"
    local monitor_config="$CONFIG_DIR/config/monitors.conf"
    
    # Remove monitor configs from main file
    sed -i '/^monitor=/d' "$main_config"
    sed -i '/^workspace=.*,monitor:/d' "$main_config"
    
    # Ensure monitor config is properly structured
    cat > "$monitor_config" << 'EOF'
# Monitor Configuration
# Format: monitor=name,resolution,position,scale

# Default monitor configuration
monitor=,preferred,auto,auto

# Specific monitor configurations
monitor=DP-1,highrr,auto,1
monitor=HDMI-A-1,preferred,auto,1

# Workspace assignments
workspace=1,monitor:DP-1
workspace=2,monitor:DP-1  
workspace=3,monitor:HDMI-A-1
workspace=4,monitor:HDMI-A-1
workspace=5,monitor:HDMI-A-1
workspace=6,monitor:HDMI-A-1
workspace=7,monitor:HDMI-A-1
EOF
    
    success "Monitor configuration conflicts resolved"
}

# Remove unused variables and clean up
clean_unused_variables() {
    log "Cleaning unused variables..."
    
    local main_config="$CONFIG_DIR/hyprland.conf"
    
    # Comment out unused script variables
    sed -i 's/^\$screenshot = /#\$screenshot = /' "$main_config"
    sed -i 's/^\$colorpicker = /#\$colorpicker = /' "$main_config"
    sed -i 's/^\$volume = /#\$volume = /' "$main_config"
    sed -i 's/^\$brightness = /#\$brightness = /' "$main_config"
    
    success "Unused variables cleaned up"
}

# Optimize window rules
optimize_window_rules() {
    log "Optimizing window rules..."
    
    local rules_file="$CONFIG_DIR/config/window-rules.conf"
    local temp_file=$(mktemp)
    
    # Remove commented lines and empty lines, then sort
    grep -v '^#' "$rules_file" | grep -v '^$' | sort > "$temp_file"
    
    # Add header back
    {
        echo "# Optimized Window Rules"
        echo "# Generated by cleanup script on $(date)"
        echo ""
        cat "$temp_file"
    } > "$rules_file"
    
    rm "$temp_file"
    success "Window rules optimized"
}

# Add missing Nvidia optimizations
add_nvidia_optimizations() {
    log "Adding Nvidia optimizations..."
    
    local main_config="$CONFIG_DIR/hyprland.conf"
    
    # Check if Nvidia optimizations are missing
    if ! grep -q "WLR_DRM_NO_ATOMIC" "$main_config"; then
        sed -i '/^env = WLR_NO_HARDWARE_CURSORS,0/a env = WLR_DRM_NO_ATOMIC,1' "$main_config"
        success "Added WLR_DRM_NO_ATOMIC optimization"
    fi
    
    if ! grep -q "NVIDIA_PRESERVE_VIDEO_MEMORY_ALLOCATIONS" "$main_config"; then
        sed -i '/^env = __GLX_VENDOR_LIBRARY_NAME,nvidia/a env = NVIDIA_PRESERVE_VIDEO_MEMORY_ALLOCATIONS,1' "$main_config"
        success "Added NVIDIA_PRESERVE_VIDEO_MEMORY_ALLOCATIONS"
    fi
}

# Optimize idle configuration
optimize_idle_config() {
    log "Optimizing idle configuration..."
    
    local idle_config="$CONFIG_DIR/hypridle.conf"
    
    cat > "$idle_config" << 'EOF'
general {
    lock_cmd = pidof hyprlock || hyprlock --immediate
    before_sleep_cmd = hyprlock --immediate
    after_sleep_cmd = hyprctl dispatch dpms on
}

# Screen lock after 10 minutes of inactivity
listener {
    timeout = 600
    on-timeout = hyprlock --immediate
}

# Turn off screen after 15 minutes
listener {
    timeout = 900
    on-timeout = hyprctl dispatch dpms off
    on-resume = hyprctl dispatch dpms on
}

# Suspend after 30 minutes (laptop only)
listener {
    timeout = 1800
    on-timeout = systemctl suspend
}
EOF
    
    success "Idle configuration optimized"
}

# Create performance monitoring script
create_performance_monitor() {
    log "Creating performance monitoring script..."
    
    cat > "$CONFIG_DIR/scripts/system/performance-monitor.sh" << 'EOF'
#!/bin/bash
# Hyprland Performance Monitor

echo "=== Hyprland Performance Report ==="
echo "Generated: $(date)"
echo

echo "--- System Resources ---"
echo "Memory usage:"
ps aux --sort=-%mem | grep -E "(hypr|waybar|rofi)" | head -10

echo -e "\nCPU usage:"
ps aux --sort=-%cpu | grep -E "(hypr|waybar|rofi)" | head -10

echo -e "\n--- Hyprland Status ---"
if pgrep -x "Hyprland" > /dev/null; then
    echo "✅ Hyprland is running"
    echo "Version: $(hyprctl version | head -1)"
    echo "Workspaces: $(hyprctl workspaces | grep -c "workspace ID")"
    echo "Windows: $(hyprctl clients | grep -c "Window")"
else
    echo "❌ Hyprland is not running"
fi

echo -e "\n--- Component Status ---"
for component in waybar rofi dunst hypridle; do
    if pgrep -x "$component" > /dev/null; then
        echo "✅ $component is running"
    else
        echo "❌ $component is not running"
    fi
done
EOF
    
    chmod +x "$CONFIG_DIR/scripts/system/performance-monitor.sh"
    success "Performance monitor created"
}

# Main execution
main() {
    echo "🧹 Hyprland Configuration Cleanup & Optimization"
    echo "================================================"
    echo
    
    # Confirm before proceeding
    warning "This script will modify your Hyprland configuration"
    read -p "Continue? (y/N): " -n 1 -r
    echo
    [[ ! $REPLY =~ ^[Yy]$ ]] && exit 1
    
    create_backup
    remove_duplicates
    fix_monitor_config
    clean_unused_variables
    optimize_window_rules
    add_nvidia_optimizations
    optimize_idle_config
    create_performance_monitor
    
    echo
    success "🎉 Optimization completed successfully!"
    echo
    echo "📋 Summary of changes:"
    echo "  • Removed duplicate files"
    echo "  • Fixed monitor configuration conflicts"
    echo "  • Cleaned unused variables"
    echo "  • Optimized window rules"
    echo "  • Added Nvidia optimizations"
    echo "  • Improved idle configuration"
    echo "  • Created performance monitor"
    echo
    echo "🔄 Restart Hyprland to apply all changes"
    echo "📊 Run performance monitor: ~/.config/hypr/scripts/system/performance-monitor.sh"
}

main "$@"
