#!/bin/bash
# Cursor Theme Selector with Rofi
# Interactive cursor theme selection

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Get available cursor themes
get_cursor_themes() {
    local themes=()
    
    # Search in system directories
    for theme_dir in /usr/share/icons/*/cursors; do
        if [[ -d "$theme_dir" ]]; then
            local theme_name=$(basename "$(dirname "$theme_dir")")
            themes+=("$theme_name")
        fi
    done
    
    # Search in user directories
    for theme_dir in "$HOME/.icons"/*/cursors; do
        if [[ -d "$theme_dir" ]]; then
            local theme_name=$(basename "$(dirname "$theme_dir")")
            themes+=("$theme_name")
        fi
    done
    
    for theme_dir in "$HOME/.local/share/icons"/*/cursors; do
        if [[ -d "$theme_dir" ]]; then
            local theme_name=$(basename "$(dirname "$theme_dir")")
            themes+=("$theme_name")
        fi
    done
    
    # Remove duplicates and sort
    printf '%s\n' "${themes[@]}" | sort -u
}

# Show cursor theme selector with rofi
show_cursor_selector() {
    local themes=$(get_cursor_themes)
    local current_theme="${XCURSOR_THEME:-oreo_black_cursors}"
    
    # Create rofi menu
    local selected_theme=$(echo "$themes" | rofi -dmenu -i \
        -p "Select Cursor Theme" \
        -theme ~/.config/rofi/config/main.rasi \
        -selected-row $(echo "$themes" | grep -n "^$current_theme$" | cut -d: -f1 | head -1))
    
    if [[ -n "$selected_theme" ]]; then
        echo -e "${BLUE}🖱️  Applying cursor theme: ${YELLOW}$selected_theme${NC}"
        
        # Apply the selected theme
        ~/.config/hypr/scripts/system/cursor-setup.sh apply "$selected_theme"
        
        # Fix any cursor issues
        ~/.config/hypr/scripts/system/cursor-fix.sh fix
        
        # Show notification
        if command -v notify-send >/dev/null 2>&1; then
            notify-send "Cursor Theme" "Applied: $selected_theme" -i input-mouse
        fi
        
        echo -e "${GREEN}✅ Cursor theme applied: $selected_theme${NC}"
    else
        echo -e "${YELLOW}⚠️  No theme selected${NC}"
    fi
}

# Quick apply function for specific themes
quick_apply() {
    local theme="$1"
    
    if [[ -z "$theme" ]]; then
        echo -e "${RED}❌ No theme specified${NC}"
        return 1
    fi
    
    echo -e "${BLUE}🖱️  Quick applying: ${YELLOW}$theme${NC}"
    ~/.config/hypr/scripts/system/cursor-setup.sh apply "$theme"
    ~/.config/hypr/scripts/system/cursor-fix.sh fix
    
    if command -v notify-send >/dev/null 2>&1; then
        notify-send "Cursor Theme" "Quick applied: $theme" -i input-mouse
    fi
}

# Show help
show_help() {
    echo -e "${CYAN}Cursor Theme Selector${NC}"
    echo "====================="
    echo
    echo -e "${YELLOW}Usage:${NC} $0 [OPTION] [THEME]"
    echo
    echo -e "${YELLOW}Options:${NC}"
    echo -e "  ${GREEN}select${NC}    Show interactive theme selector (default)"
    echo -e "  ${GREEN}quick${NC}     Quick apply specific theme"
    echo -e "  ${GREEN}list${NC}      List available themes"
    echo -e "  ${GREEN}help${NC}      Show this help"
    echo
    echo -e "${YELLOW}Examples:${NC}"
    echo -e "  $0 select                    # Interactive selector"
    echo -e "  $0 quick oreo_black_cursors  # Quick apply"
    echo -e "  $0 list                      # List themes"
    echo
}

# List available themes
list_themes() {
    echo -e "${CYAN}📁 Available Cursor Themes:${NC}"
    echo "============================"
    echo
    
    local themes=$(get_cursor_themes)
    local count=0
    
    while IFS= read -r theme; do
        count=$((count + 1))
        
        # Check if it's the current theme
        if [[ "$theme" == "${XCURSOR_THEME:-oreo_black_cursors}" ]]; then
            echo -e "  ${GREEN}$count. $theme ${YELLOW}(current)${NC}"
        else
            echo -e "  ${BLUE}$count.${NC} $theme"
        fi
    done <<< "$themes"
    
    echo
    echo -e "${BLUE}📊 Total themes: $count${NC}"
}

# Main execution
case "${1:-select}" in
    select)
        show_cursor_selector
        ;;
    quick)
        quick_apply "$2"
        ;;
    list)
        list_themes
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}❌ Unknown option: $1${NC}"
        echo
        show_help
        exit 1
        ;;
esac
