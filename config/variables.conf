# ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
# ┃                    Variables Configuration                  ┃
# ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

source = ~/.config/hypr/config/colors.conf

# Variables wiki https://wiki.hyprland.org/0.45.0/Configuring/Variables/ #
# https://wiki.hyprland.org/0.45.0/Configuring/Variables/#general
general {

    gaps_in = 3
    gaps_out = 5
    border_size = 1
    col.active_border = $cachylgreen
    col.inactive_border = $cachymblue
    layout = dwindle # master|dwindle

    # https://wiki.hyprland.org/0.45.0/Configuring/Variables/#snap
    snap {
    	enabled = true
    }

}

# https://wiki.hyprland.org/0.45.0/Configuring/Variables/#gestures
gestures {
     workspace_swipe = true
     workspace_swipe_fingers = 4
     workspace_swipe_distance = 250
     workspace_swipe_min_speed_to_force = 15
     workspace_swipe_create_new = false
}

# https://wiki.hyprland.org/0.45.0/Configuring/Variables/#group
group {
	col.border_active = $cachydgreen
	col.border_inactive = $cachylgreen
	col.border_locked_active = $cachymgreen
	col.border_locked_inactive = $cachydblue

    # https://wiki.hyprland.org/0.45.0/Configuring/Variables/#groupbar
	groupbar {
		font_family = "Fira Sans"
		text_color = $cachydblue
		col.active = $cachydgreen
		col.inactive = $cachylgreen
		col.locked_active = $cachymgreen
		col.locked_inactive = $cachydblue
	}
}
# https://wiki.hyprland.org/0.45.0/Configuring/Variables/#misc
misc {
    font_family = "Fira Sans"
    splash_font_family = "Fira Sans"
    disable_hyprland_logo = true
    col.splash = $cachylgreen
    background_color = $cachydblue
    enable_swallow = true
    swallow_regex = ^(cachy-browser|firefox|nautilus|nemo|thunar|btrfs-assistant.)$
    focus_on_activate = true
    vrr = 2
}

# https://wiki.hyprland.org/0.45.0/Configuring/Variables/#render
render {
   direct_scanout = true
}

# See https://wiki.hyprland.org/0.45.0/Configuring/Dwindle-Layout/ for more
dwindle {
    special_scale_factor = 0.8
    pseudotile = true # master switch for pseudotiling. Enabling is bound to mainMod + P in the keybinds section below
    preserve_split = true
}

# See https://wiki.hyprland.org/0.45.0/Configuring/Master-Layout/ for more
master {
    new_status = master
    special_scale_factor = 0.8
}
