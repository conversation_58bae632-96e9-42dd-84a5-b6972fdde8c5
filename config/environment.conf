# █▀▀ █▄░█ █░█ █ █▀█ █▀█ █▄░█ █▀▄▀█ █▀▀ █▄░█ ▀█▀
# █▀░ █░▀█ ▀▄▀ █ █▀▄ █▄█ █░▀█ █░▀░█ ██▄ █░▀█ ░█░
# Environment Variables

# Qt/GTK
env = QT_QPA_PLATFORMTHEME,qt5ct
env = QT_QPA_PLATFORM,wayland
env = QT_WAYLAND_DISABLE_WINDOWDECORATION,1
env = QT_AUTO_SCREEN_SCALE_FACTOR,1
# GTK
env = GDK_BACKEND,wayland,x11

# XDG
env = XDG_CURRENT_DESKTOP,Hyprland
env = XDG_SESSION_TYPE,wayland
env = XDG_SESSION_DESKTOP,Hyprland

# Hardware video acceleration
env = LIBVA_DRIVER_NAME,nvidia
env = GBM_BACKEND,nvidia-drm

# Nvidia specific
env = __GLX_VENDOR_LIBRARY_NAME,nvidia
env = NVIDIA_PRESERVE_VIDEO_MEMORY_ALLOCATIONS,1
env = WLR_NO_HARDWARE_CURSORS,0
env = WLR_DRM_NO_ATOMIC,1

envd = HYPRCURSOR_SIZE,24
envd = XCURSOR_SIZE,24
envd = QT_CURSOR_SIZE,24

# Cursor theme configuration
env = HYPRCURSOR_THEME,Sweet-cursors
env = XCURSOR_THEME,Sweet-cursors
env = QT_CURSOR_THEME,Sweet-cursors
