# Optimized Window Rules
# Generated by cleanup script on jue 17 jul 2025 21:10:28 -05

windowrule = animations off, class:^(pcsx2-qt|Steam|lutris|<PERSON><PERSON>s|heroic|yuzu|<PERSON><PERSON>|retroarch|<PERSON><PERSON>|rpcs3|chiaki|net.lutris.Lutris)$
windowrule = center, class:Minecraft
windowrule = float, class:Minecraft
windowrule = maxsize 1 1, class:^(xwaylandvideobridge)$
windowrule = move 0 0, title:^(Firefox)(.*)$
windowrule = move 963 43, class:batt.py
windowrule = noanim, class:^(xwaylandvideobridge)$
windowrule = noblur, class:^(xwaylandvideobridge)$
windowrule = nofocus, class:^(xwaylandvideobridge)$
windowrule = noinitialfocus, class:^(xwaylandvideobridge)$
windowrule = opacity 0.0 override, class:^(xwaylandvideobridge)$
windowrule = size 1600 900, class:Minecraft
windowrule = workspace 1, class:^(kitty|<PERSON><PERSON>ritty|foot)$
windowrule = workspace 2, class:^(code|Code|VSCodium|codium-url-handler|Geany|code-oss|jetbrains-idea-ce)$
windowrule = workspace 3, class:^(zen|firefox|Firefox-esr|brave-browser|Brave-browser|Google-chrome|Chromium|Vivaldi-stable)$
windowrule = workspace 4, class:^(discord|WebCord|org.telegram.desktop|telegram-desktop|whatsapp-nativefier-d40211|whatsdesk)$
windowrule = workspace 5, class:^(vlc|Spotify|mpv|eog|gwenview|org.kde.gwenview|libreoffice|Libreoffice|libreoffice-writer|libreoffice-calc|libreoffice-impress|evince|okular|obsidian)$
windowrule = workspace 6, class:^(org.pulseaudio.pavucontrol|org.gnome.Nautilus|nemo|thunar|org.kde.dolphin|org.gnome.nautilus|pavucontrol|blueman-manager|nm-connection-editor|org.kde.polkit-kde-authentication-agent-1|fdm)$
windowrule = workspace 7, class:^(pcsx2-qt|Steam|lutris|Lutris|heroic|yuzu|Yuzu|retroarch|Cemu|rpcs3|chiaki|net.lutris.Lutris)$
windowrule =noanim,noblur,nofocus,opacity w1 override 1 class:^(jetbrains-idea-ce)$
windowrulev2 = center, class:blueman-manager
windowrulev2 = center, class:(dolphin), title:^(Choose.*)$
windowrulev2 = center, class:dolphin, title:^(File Already Exists)$
windowrulev2 = center, class:(dolphin), title:^(Select.*)$
windowrulev2 = center, class:eog
windowrulev2 = center, class:gwenview
windowrulev2 = center, class:kitty, title:^(float)$
windowrulev2 = center, class:lutris
windowrulev2 = center, class:(nemo), title:^(Choose.*)$
windowrulev2 = center, class:nemo, title:^(File Operations)$
windowrulev2 = center, class:(nemo), title:^(Select.*)$
windowrulev2 = center, class:nm-connection-editor
windowrulev2 = center, class:nwg-look
windowrulev2 = center, class:org.kde.kdialog
windowrulev2 = center, class:org.kde.polkit-kde-authentication-agent-1
windowrulev2 = center, class:org.telegram-desktop
windowrulev2 = center, class:pavucontr
windowrulev2 = center, class:qt5ct
windowrulev2 = center, class:Steam
windowrulev2 = center, class:(thunar), title:^(Choose.*)$
windowrulev2 = center, class:thunar, title:^(File Operation Progress)$
windowrulev2 = center, class:(thunar), title:^(Select.*)$
windowrulev2 = center, class:Xdg-desktop-portal-gtk
windowrulev2 = center, title:^(Choose Files)$
windowrulev2 = center, title:^(Open File)$
windowrulev2 = center, title:^(Open Folder)$
windowrulev2 = center, title:^(Open)$
windowrulev2 = center, title:^(Save As)$
windowrulev2 = center,float, title:("Registro")
windowrulev2 = float, class:^(batt.py)$
windowrulev2 = float, class:blueman-manager
windowrulev2 = float, class:(dolphin), title:^(Choose.*)$
windowrulev2 = float, class:dolphin, title:^(File Already Exists)$
windowrulev2 = float, class:(dolphin), title:^(Select.*)$
windowrulev2 = float, class:eog
windowrulev2 = float, class:flet
windowrulev2 = float, class:Flet
windowrulev2 = float, class:gwenview
windowrulev2 = float, class:kitty, title:^(float)$
windowrulev2 = float, class:lutris
windowrulev2 = float, class:(nemo), title:^(Choose.*)$
windowrulev2 = float, class:nemo, title:^(File Operations)$
windowrulev2 = float, class:(nemo), title:^(Select.*)$
windowrulev2 = float, class:nm-connection-editor
windowrulev2 = float, class:nwg-look
windowrulev2 = float, class:org.kde.kdialog
windowrulev2 = float, class:org.kde.polkit-kde-authentication-agent-1
windowrulev2 = float, class:pavucontrol
windowrulev2 = float, class:qt5ct
windowrulev2 = float, class:Steam
windowrulev2 = float, class:(thunar), title:^(Choose.*)$
windowrulev2 = float, class:thunar, title:^(File Operation Progress)$
windowrulev2 = float, class:(thunar), title:^(Select.*)$
windowrulev2 = float, move 963 716, class:^(org.telegram.desktop)$
windowrulev2 = float, title:^(Ajustes de control de PCSX2)$
windowrulev2 = float, title:^(Choose Files)$
windowrulev2 = float, title:DeskTranslate
windowrulev2 = float, title:DeskTranslate v1.1
windowrulev2 = float, title:^(Inicia sesión: Cuentas de Google — Mozilla Firefox)$
windowrulev2 = float, title:^(Open File)$
windowrulev2 = float, title:^(Open Folder)$
windowrulev2 = float, title:^(Open)$
windowrulev2 = float, title:Play Translate - Traductor en Tiempo Real
windowrulev2 = float, title:^(Save As)$
windowrulev2 = float, title:^(Van Helsing [SLES-51908_8176235A.ini])$
windowrulev2 = float,class:mainSideUi.py
windowrulev2 = noborder,title:Traductor
windowrulev2 = opacity 0.8 0.8, class:nemo
windowrulev2 = opacity 0.90 0.95, class:org.manjaro.pamac.manager
windowrulev2 = opacity 0.95 0.95, class:batt.py
windowrulev2 = opacity 0.95 0.95, class:file-roller
windowrulev2 = opacity 0.95 0.95, class:org.freedesktop.impl.portal.desktop.kde
windowrulev2 = opacity 0.95 0.95, class:org.gnome.Nautilus
windowrulev2 = opacity 0.95 0.95, class:org.kde.dolphin
windowrulev2 = opacity 0.95 0.95, class:org.kde.polkit-kde-authentication-agent-1
windowrulev2 = opacity 0.95 0.95, class:^(org.telegram.desktop|discord|WebCord|telegram-desktop|whatsapp-nativefier-d40211|whatsdesk)$
windowrulev2 = opacity 0.95 0.95, class:swaync-control-center
windowrulev2 = opacity 0.95 0.95, class:swaync-notification-window
windowrulev2 = opacity 0.95 0.95, class:thunar
windowrulev2 = opacity 0.95 0.95, class:^(wofi|nwg-look|qt5ct|pavucontrol|swaync-notification-window|swaync-control-center|thunar)$
windowrulev2 = opacity 0.95 0.95, class:Xdg-desktop-portal-gtk
windowrulev2 = opacity 0.95, class:obsidian
windowrulev2 = opacity 0.99 0.99, class:^(firefox|brave-browser|Brave-browser|Google-chrome|Chromium|Vivaldi-stable)$
windowrulev2 = opacity 1, class:jetbrains-idea-ce
windowrulev2 = size 1600 900, class:Steam
windowrulev2 = size 400 100, class:dolphin, title:^(File Already Exists)$
windowrulev2 = size 400 100, class:nemo, title:^(File Operations)$
windowrulev2 = size 400 100, class:thunar, title:^(File Operation Progress)$
windowrulev2 = size 500 300, class:org.kde.polkit-kde-authentication-agent-1
windowrulev2 = size 600 600, class:blueman-manager
windowrulev2 = size 600 600, class:nm-connection-editor
windowrulev2 = size 600 600, class:pavucontrol
windowrulev2 = size 960 600, class:(dolphin), title:^(Choose.*)$
windowrulev2 = size 960 600, class:(dolphin), title:^(Select.*)$
windowrulev2 = size 960 600, class:eog
windowrulev2 = size 960 600, class:gwenview
windowrulev2 = size 960 600, class:lutris
windowrulev2 = size 960 600, class:(nemo), title:^(Choose.*)$
windowrulev2 = size 960 600, class:(nemo), title:^(Select.*)$
windowrulev2 = size 960 600, class:org.kde.kdialog
windowrulev2 = size 960 600, class:(thunar), title:^(Choose.*)$
windowrulev2 = size 960 600, class:(thunar), title:^(Select.*)$
windowrulev2 = size 960 600, title:^(Choose Files)$
windowrulev2 = size 960 600, title:^(Open File)$
windowrulev2 = size 960 600, title:^(Open Folder)$
windowrulev2 = size 960 600, title:^(Open)$
windowrulev2 = size 960 600, title:^(Save As)$
windowrulev2 = size 960 700, class:kitty, title:^(float)$
windowrulev2 = size 960 700, class:nwg-look
windowrulev2 = size 960 700, class:qt5ct
windowrulev2 = suppressevent maximize, class:.*
