# Launch favorite apps
exec-once = swww-daemon & # Wallpaper daemon
exec-once = ~/.config/hypr/scripts/tools/AleatoryWall.sh &
exec-once = ~/.config/hypr/scripts/system/Autostarts.sh waybar --waybar &

# Cursor theme setup
exec-once = ~/.config/hypr/scripts/system/cursor-setup.sh apply &

# Core components for system functionality
exec-once = gnome-keyring-daemon --start --components=secrets,ssh # Authentication
exec-once = /usr/lib/polkit-gnome/polkit-gnome-authentication-agent-1 # Authentication agent
exec-once = dbus-update-activation-environment --all # Update environment variables
exec-once = sleep 1 && dbus-update-activation-environment --systemd WAYLAND_DISPLAY XDG_CURRENT_DESKTOP # Environment fix
exec-once = udiskie & # Automount disks
exec-once = systemctl --user import-environment WAYLAND_DISPLAY XDG_CURRENT_DESKTOP # Import environment variables
exec-once = hypridle

# Notification daemon
exec-once = ~/.config/dunst/wal-dunstrc-gen.sh

# Idle configuration
exec-once = ~/.config/hypr/scripts/system/idle-config.sh &

# XDG Portal setup
exec-once = ~/.config/hypr/scripts/system/xdgportal.sh &
exec-once = systemctl --user restart xdg-desktop-portal.service

# Gestor de Energia

exec-once = xfce4-power-manager &
