# Set programs that you use
$terminal = kitty
$fileManager = nautilus
$menu = fuzzel
$code = vscodium
$browser = firefox
$launcher = $HOME/.config/hypr/rofi/scripts/utilities/allApps

# Launch applications
bind = $mainMod, Return, exec, $terminal
bind = $mainMod, E, exec, $fileManager
bind = $mainMod, R, exec, $menu
bind = $mainMod, C, exec, $code
bind = $mainMod, B, exec, $browser
bind = $mainMod, D, exec, $launcher

# Window management
bind = $mainMod, Q, killactive,
bind = $mainMod, M, exit,
bind = $mainMod, V, togglefloating,
bind = $mainMod, P, pseudo,      # dwindle
bind = $mainMod, J, togglesplit, # dwindle
bind = $mainMod alt,Tab,cyclenext,
bind = $mainMod, Tab, workspace, prev
# Move focus with mainMod + arrow keys
bind = $mainMod, left, movefocus, l
bind = $mainMod, right, movefocus, r
bind = $mainMod, up, movefocus, u
bind = $mainMod, down, movefocus, d

# Switch workspaces with mainMod + [0-7]
bind = $mainMod, 1, workspace, 1
bind = $mainMod, 2, workspace, 2
bind = $mainMod, 3, workspace, 3
bind = $mainMod, 4, workspace, 4
bind = $mainMod, 5, workspace, 5
bind = $mainMod, 6, workspace, 6
bind = $mainMod, 7, workspace, 7

# Move active window to a workspace with mainMod + SHIFT + [0-7]
bind = $mainMod SHIFT, 1, movetoworkspace, 1
bind = $mainMod SHIFT, 2, movetoworkspace, 2
bind = $mainMod SHIFT, 3, movetoworkspace, 3
bind = $mainMod SHIFT, 4, movetoworkspace, 4
bind = $mainMod SHIFT, 5, movetoworkspace, 5
bind = $mainMod SHIFT, 6, movetoworkspace, 6
bind = $mainMod SHIFT, 7, movetoworkspace, 7

# Example special workspace (scratchpad)
bind = $mainMod, S, togglespecialworkspace, magic
bind = $mainMod SHIFT, S, movetoworkspace, special:magic

# Scroll through existing workspaces with mainMod + scroll
bind = $mainMod, mouse_down, workspace, e+1
bind = $mainMod, mouse_up, workspace, e-1

# Move/resize windows with mainMod + LMB/RMB and dragging
bindm = $mainMod, mouse:272, movewindow
bindm = $mainMod, mouse:273, resizewindow

# ----- System ----- #

# Lock screen
bind = $mainMod, L, exec, swaylock-fancy

# Screenshot
bind = $mainMod, PRINT, exec, hyprshot -m window
bind = , PRINT, exec, hyprshot -m output
bind = $shiftMod, PRINT, exec, hyprshot -m region

# Waybar restart
bind = $mainMod SHIFT, R, exec, ~/.config/hypr/scripts/Autostarts.sh waybar --waybar

# Clipboard
bind = $mainMod ALT, C, exec, wl-paste | xargs -I{} $code {}

# Volume and brightness control
bind = , XF86AudioRaiseVolume, exec, pactl set-sink-volume @DEFAULT_SINK@ +5%
bind = , XF86AudioLowerVolume, exec, pactl set-sink-volume @DEFAULT_SINK@ -5%
bind = , XF86AudioMute, exec, pactl set-sink-mute @DEFAULT_SINK@ toggle
bind = , XF86MonBrightnessUp, exec, brightnessctl set +10%
bind = , XF86MonBrightnessDown, exec, brightnessctl set 10%-

# Wallpaper controls
bind = $mainMod SHIFT, W, exec, ~/.config/hypr/scripts/tools/wallpaper-control.sh change && notify-send "Wallpaper changed"
bind = $mainMod CTRL, W, exec, ~/.config/hypr/scripts/tools/wallpaper-control.sh restart && notify-send "Wallpaper daemon restarted"
bind = $mainMod ALT, W, exec, ~/.config/hypr/scripts/tools/wallpaper-control.sh status && notify-send "Wallpaper daemon status"

# Cursor theme controls
bind = $mainMod SHIFT, C, exec, ~/.config/hypr/scripts/system/cursor-selector.sh select
bind = $mainMod CTRL, C, exec, ~/.config/hypr/scripts/system/cursor-fix.sh fix && notify-send "Cursor fixed"

# Enhanced functionality keybindings
bind = $mainMod SHIFT, M, exec, ~/.config/hypr/scripts/tools/monitor-manager.sh
bind = $mainMod SHIFT, P, exec, ~/.config/hypr/scripts/tools/profile-manager.sh work
bind = $mainMod SHIFT, G, exec, ~/.config/hypr/scripts/tools/profile-manager.sh gaming
bind = $mainMod SHIFT, O, exec, ~/.config/hypr/scripts/tools/workspace-manager.sh organize
bind = $mainMod SHIFT, H, exec, ~/.config/hypr/scripts/system/health-monitor.sh && notify-send "Health Check" "System health logged"
